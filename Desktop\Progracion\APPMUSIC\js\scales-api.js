// Servicio de API para escalas usando Tonal.js
// Este archivo maneja la generación de escalas para guitarra, piano y bajo

// Importar Tonal.js desde CDN (se carga en el HTML)
// https://cdn.jsdelivr.net/npm/tonal@5.0.0/browser/tonal.min.js

class ScalesApiService {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 300000; // 5 minutos
        
        // Configuración de afinaciones
        this.tunings = {
            guitar: ['E', 'A', 'D', 'G', 'B', 'E'], // De la 6ª a la 1ª cuerda
            bass: ['E', 'A', 'D', 'G'], // De la 4ª a la 1ª cuerda
            piano: null // No aplica afinación
        };

        // Escalas disponibles
        this.availableScales = [
            'major', 'minor', 'dorian', 'phrygian', 'lydian', 
            'mixolydian', 'aeolian', 'locrian', 'pentatonic major', 
            'pentatonic minor', 'blues', 'harmonic minor', 'melodic minor'
        ];
    }

    // Verificar si Tonal.js está disponible
    isTonalAvailable() {
        return typeof Tonal !== 'undefined';
    }

    // Generar clave de caché
    generateCacheKey(root, scaleName, instrument) {
        return `${root}-${scaleName}-${instrument}`;
    }

    // Obtener datos del caché
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;

        // Verificar si ha expirado
        if (Date.now() - cached.timestamp > this.cacheTimeout) {
            this.cache.delete(key);
            return null;
        }

        return cached.data;
    }

    // Guardar en caché
    saveToCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    // Obtener escala usando Tonal.js
    getScale(root, scaleName) {
        if (!this.isTonalAvailable()) {
            console.warn('Tonal.js no está disponible');
            return this.getFallbackScale(root, scaleName);
        }

        try {
            // Usar Tonal.js para generar la escala
            const scale = Tonal.Scale.get(`${root} ${scaleName}`);
            
            if (scale.notes && scale.notes.length > 0) {
                return {
                    name: scale.name || `${root} ${scaleName}`,
                    notes: scale.notes,
                    intervals: scale.intervals || [],
                    type: scaleName,
                    root: root
                };
            } else {
                return this.getFallbackScale(root, scaleName);
            }
        } catch (error) {
            console.error('Error al generar escala con Tonal.js:', error);
            return this.getFallbackScale(root, scaleName);
        }
    }

    // Escala de fallback si Tonal.js no está disponible
    getFallbackScale(root, scaleName) {
        const scalePatterns = {
            'major': [0, 2, 4, 5, 7, 9, 11],
            'minor': [0, 2, 3, 5, 7, 8, 10],
            'dorian': [0, 2, 3, 5, 7, 9, 10],
            'pentatonic major': [0, 2, 4, 7, 9],
            'pentatonic minor': [0, 3, 5, 7, 10],
            'blues': [0, 3, 5, 6, 7, 10]
        };

        const pattern = scalePatterns[scaleName] || scalePatterns['major'];
        const notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
        const rootIndex = notes.indexOf(root);

        if (rootIndex === -1) return null;

        const scaleNotes = pattern.map(interval => {
            const noteIndex = (rootIndex + interval) % 12;
            return notes[noteIndex];
        });

        return {
            name: `${root} ${scaleName}`,
            notes: scaleNotes,
            intervals: pattern,
            type: scaleName,
            root: root
        };
    }

    // Generar posiciones para guitarra
    generateGuitarPositions(scale) {
        const positions = [];
        const tuning = this.tunings.guitar;
        const notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

        // Generar posiciones en diferentes trastes
        for (let startFret = 0; startFret <= 12; startFret += 3) {
            const position = {
                name: `Posición ${Math.floor(startFret / 3) + 1}`,
                startFret: startFret,
                strings: []
            };

            // Para cada cuerda
            tuning.forEach((openNote, stringIndex) => {
                const openNoteIndex = notes.indexOf(openNote);
                const stringPositions = [];

                // Buscar notas de la escala en esta cuerda
                for (let fret = startFret; fret < startFret + 5; fret++) {
                    const noteIndex = (openNoteIndex + fret) % 12;
                    const note = notes[noteIndex];

                    if (scale.notes.includes(note)) {
                        stringPositions.push({
                            fret: fret,
                            note: note,
                            isRoot: note === scale.root
                        });
                    }
                }

                position.strings.push({
                    string: 6 - stringIndex, // Numeración de cuerdas
                    openNote: openNote,
                    positions: stringPositions
                });
            });

            // Solo agregar posiciones que tengan notas
            if (position.strings.some(s => s.positions.length > 0)) {
                positions.push(position);
            }
        }

        return positions;
    }

    // Generar posiciones para piano
    generatePianoPositions(scale) {
        const positions = [];
        
        // Posición básica
        const basicPosition = {
            name: 'Posición básica',
            octave: 4,
            rightHand: {
                notes: scale.notes.map((note, index) => `${note}${4 + Math.floor(index / 7)}`),
                fingering: this.generatePianoFingering(scale.notes.length)
            },
            leftHand: {
                notes: scale.notes.map((note, index) => `${note}${3 + Math.floor(index / 7)}`),
                fingering: this.generatePianoFingering(scale.notes.length, true)
            }
        };

        positions.push(basicPosition);

        return positions;
    }

    // Generar digitación para piano
    generatePianoFingering(noteCount, leftHand = false) {
        if (leftHand) {
            // Digitación para mano izquierda (invertida)
            const pattern = [5, 4, 3, 2, 1, 3, 2, 1];
            return pattern.slice(0, noteCount).join('-');
        } else {
            // Digitación para mano derecha
            const pattern = [1, 2, 3, 1, 2, 3, 4, 5];
            return pattern.slice(0, noteCount).join('-');
        }
    }

    // Generar posiciones para bajo
    generateBassPositions(scale) {
        const positions = [];
        const tuning = this.tunings.bass;
        const notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

        // Generar posiciones básicas
        for (let startFret = 0; startFret <= 12; startFret += 4) {
            const position = {
                name: `Posición ${Math.floor(startFret / 4) + 1}`,
                startFret: startFret,
                strings: []
            };

            // Para cada cuerda del bajo
            tuning.forEach((openNote, stringIndex) => {
                const openNoteIndex = notes.indexOf(openNote);
                const stringPositions = [];

                // Buscar notas de la escala en esta cuerda
                for (let fret = startFret; fret < startFret + 5; fret++) {
                    const noteIndex = (openNoteIndex + fret) % 12;
                    const note = notes[noteIndex];

                    if (scale.notes.includes(note)) {
                        stringPositions.push({
                            fret: fret,
                            note: note,
                            isRoot: note === scale.root
                        });
                    }
                }

                position.strings.push({
                    string: 4 - stringIndex, // Numeración de cuerdas
                    openNote: openNote,
                    positions: stringPositions
                });
            });

            // Solo agregar posiciones que tengan notas
            if (position.strings.some(s => s.positions.length > 0)) {
                positions.push(position);
            }
        }

        return positions;
    }

    // API principal: obtener escala para un instrumento
    async getScaleForInstrument(root, scaleName, instrument) {
        const cacheKey = this.generateCacheKey(root, scaleName, instrument);
        
        // Verificar caché
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return {
                success: true,
                data: cached,
                source: 'cache'
            };
        }

        try {
            // Generar escala base
            const scale = this.getScale(root, scaleName);
            
            if (!scale) {
                return {
                    success: false,
                    error: `No se pudo generar la escala ${root} ${scaleName}`
                };
            }

            // Generar posiciones según el instrumento
            let positions = [];
            switch (instrument) {
                case 'guitar':
                    positions = this.generateGuitarPositions(scale);
                    break;
                case 'piano':
                    positions = this.generatePianoPositions(scale);
                    break;
                case 'bass':
                    positions = this.generateBassPositions(scale);
                    break;
                default:
                    return {
                        success: false,
                        error: `Instrumento no soportado: ${instrument}`
                    };
            }

            // Generar imagen de la escala
            const scaleImage = this.generateScaleImage(scale, instrument);

            const result = {
                scale: scale,
                instrument: instrument,
                positions: positions,
                imageUrl: scaleImage,
                generated: new Date().toISOString()
            };

            // Guardar en caché
            this.saveToCache(cacheKey, result);

            return {
                success: true,
                data: result,
                source: 'generated'
            };

        } catch (error) {
            console.error('Error al generar escala:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Obtener lista de escalas disponibles
    getAvailableScales() {
        return this.availableScales;
    }

    // Generar imagen SVG de la escala
    generateScaleImage(scale, instrument) {
        const width = 400;
        const height = 300;

        let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;

        // Fondo
        svg += `<rect width="${width}" height="${height}" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="8"/>`;

        // Título
        svg += `<text x="${width/2}" y="30" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#495057">${scale.name}</text>`;

        // Notas de la escala
        const notesText = `Notas: ${scale.notes.join(' - ')}`;
        svg += `<text x="${width/2}" y="60" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">${notesText}</text>`;

        if (instrument === 'guitar' || instrument === 'bass') {
            // Dibujar diapasón simplificado
            svg += this.drawFretboard(scale, instrument, 80, width - 40, height - 120);
        } else if (instrument === 'piano') {
            // Dibujar teclado simplificado
            svg += this.drawKeyboard(scale, 80, width - 40, height - 120);
        }

        svg += '</svg>';

        // Convertir a data URL
        return `data:image/svg+xml;base64,${btoa(svg)}`;
    }

    // Dibujar diapasón para guitarra/bajo
    drawFretboard(scale, instrument, y, width, height) {
        const strings = instrument === 'guitar' ? 6 : 4;
        const frets = 12;
        const stringSpacing = height / (strings + 1);
        const fretSpacing = width / (frets + 1);

        let svg = '';

        // Dibujar cuerdas
        for (let i = 1; i <= strings; i++) {
            const stringY = y + i * stringSpacing;
            svg += `<line x1="40" y1="${stringY}" x2="${width + 20}" y2="${stringY}" stroke="#6c757d" stroke-width="1"/>`;
        }

        // Dibujar trastes
        for (let i = 0; i <= frets; i++) {
            const fretX = 40 + i * fretSpacing;
            svg += `<line x1="${fretX}" y1="${y + stringSpacing}" x2="${fretX}" y2="${y + strings * stringSpacing}" stroke="#495057" stroke-width="${i === 0 ? 3 : 1}"/>`;
        }

        // Marcar notas de la escala
        const tuning = instrument === 'guitar' ? ['E', 'A', 'D', 'G', 'B', 'E'] : ['E', 'A', 'D', 'G'];
        const notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

        tuning.forEach((openNote, stringIndex) => {
            const openNoteIndex = notes.indexOf(openNote);
            const stringY = y + (stringIndex + 1) * stringSpacing;

            for (let fret = 0; fret < frets; fret++) {
                const noteIndex = (openNoteIndex + fret) % 12;
                const note = notes[noteIndex];

                if (scale.notes.includes(note)) {
                    const fretX = 40 + fret * fretSpacing + fretSpacing / 2;
                    const isRoot = note === scale.root;

                    svg += `<circle cx="${fretX}" cy="${stringY}" r="8" fill="${isRoot ? '#dc3545' : '#007bff'}" stroke="#fff" stroke-width="2"/>`;
                    svg += `<text x="${fretX}" y="${stringY + 4}" text-anchor="middle" font-family="Arial" font-size="10" font-weight="bold" fill="white">${note}</text>`;
                }
            }
        });

        return svg;
    }

    // Dibujar teclado para piano
    drawKeyboard(scale, y, width, height) {
        const whiteKeys = 14; // 2 octavas
        const keyWidth = width / whiteKeys;
        const whiteKeyHeight = height * 0.8;
        const blackKeyHeight = height * 0.5;
        const blackKeyWidth = keyWidth * 0.6;

        let svg = '';

        // Dibujar teclas blancas
        const whiteNotes = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];
        for (let i = 0; i < whiteKeys; i++) {
            const x = 40 + i * keyWidth;
            const note = whiteNotes[i % 7];
            const isInScale = scale.notes.includes(note);
            const isRoot = note === scale.root;

            let fill = '#f8f9fa';
            if (isInScale) {
                fill = isRoot ? '#dc3545' : '#007bff';
            }

            svg += `<rect x="${x}" y="${y}" width="${keyWidth}" height="${whiteKeyHeight}" fill="${fill}" stroke="#495057" stroke-width="1"/>`;

            if (isInScale) {
                svg += `<text x="${x + keyWidth/2}" y="${y + whiteKeyHeight - 20}" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="white">${note}</text>`;
            }
        }

        // Dibujar teclas negras
        const blackKeyPositions = [0.7, 1.7, 3.7, 4.7, 5.7]; // Posiciones relativas
        const blackNotes = ['C#', 'D#', 'F#', 'G#', 'A#'];

        for (let octave = 0; octave < 2; octave++) {
            blackKeyPositions.forEach((pos, index) => {
                const x = 40 + (pos + octave * 7) * keyWidth - blackKeyWidth / 2;
                const note = blackNotes[index];
                const isInScale = scale.notes.includes(note);
                const isRoot = note === scale.root;

                let fill = '#495057';
                if (isInScale) {
                    fill = isRoot ? '#dc3545' : '#007bff';
                }

                svg += `<rect x="${x}" y="${y}" width="${blackKeyWidth}" height="${blackKeyHeight}" fill="${fill}" stroke="#343a40" stroke-width="1"/>`;

                if (isInScale) {
                    svg += `<text x="${x + blackKeyWidth/2}" y="${y + blackKeyHeight - 10}" text-anchor="middle" font-family="Arial" font-size="10" font-weight="bold" fill="white">${note}</text>`;
                }
            });
        }

        return svg;
    }

    // Limpiar caché
    clearCache() {
        this.cache.clear();
    }
}

// Crear instancia global
window.ScalesApiService = new ScalesApiService();
