// JavaScript principal - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Navegación móvil
    const burger = document.querySelector('.burger');
    const nav = document.querySelector('.nav-links');
    
    if (burger && nav) {
        burger.addEventListener('click', function() {
            nav.classList.toggle('active');
            burger.classList.toggle('active');
        });
    }
    
    // Cerrar menú al hacer clic en un enlace
    const navLinks = document.querySelectorAll('.nav-links a');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (nav.classList.contains('active')) {
                nav.classList.remove('active');
                burger.classList.remove('active');
            }
        });
    });
    
    // Cambiar tabs en las páginas que los usan
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            const tabContent = document.querySelector(`.${tabName}-tab`);
            
            // Desactivar todos los botones y contenidos
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Activar el botón y contenido seleccionado
            this.classList.add('active');
            if (tabContent) {
                tabContent.classList.add('active');
            }
        });
    });
    
    // Botones de mostrar/ocultar contraseña
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const passwordInput = this.previousElementSibling;
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                this.classList.remove('fa-eye');
                this.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                this.classList.remove('fa-eye-slash');
                this.classList.add('fa-eye');
            }
        });
    });
    
    // Botones de acción en secciones vacías
    const emptyActionButtons = document.querySelectorAll('.empty-actions button[data-tab]');
    emptyActionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            const tabButton = document.querySelector(`.tab-btn[data-tab="${tabName}"]`);
            
            if (tabButton) {
                tabButton.click();
            }
        });
    });
});

// Función para mostrar mensajes de alerta
function showAlert(message, type = 'info') {
    // Crear el elemento de alerta
    const alertElement = document.createElement('div');
    alertElement.className = `alert alert-${type}`;
    alertElement.innerHTML = `
        <div class="alert-content">
            <p>${message}</p>
            <button class="alert-close"><i class="fas fa-times"></i></button>
        </div>
    `;
    
    // Añadir al DOM
    document.body.appendChild(alertElement);
    
    // Mostrar con animación
    setTimeout(() => {
        alertElement.classList.add('show');
    }, 10);
    
    // Configurar cierre
    const closeButton = alertElement.querySelector('.alert-close');
    closeButton.addEventListener('click', () => {
        alertElement.classList.remove('show');
        setTimeout(() => {
            alertElement.remove();
        }, 300);
    });
    
    // Auto-cerrar después de 5 segundos
    setTimeout(() => {
        if (document.body.contains(alertElement)) {
            alertElement.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(alertElement)) {
                    alertElement.remove();
                }
            }, 300);
        }
    }, 5000);
}

// Función para validar formularios
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            isValid = false;
            input.classList.add('error');
            
            // Mostrar mensaje de error
            const errorMessage = input.getAttribute('data-error') || 'Este campo es obligatorio';
            let errorElement = input.nextElementSibling;
            
            if (!errorElement || !errorElement.classList.contains('error-message')) {
                errorElement = document.createElement('div');
                errorElement.className = 'error-message';
                input.parentNode.insertBefore(errorElement, input.nextSibling);
            }
            
            errorElement.textContent = errorMessage;
        } else {
            input.classList.remove('error');
            const errorElement = input.nextElementSibling;
            if (errorElement && errorElement.classList.contains('error-message')) {
                errorElement.remove();
            }
        }
    });
    
    return isValid;
}

// Función para simular carga de datos
function simulateLoading(element, callback, time = 1000) {
    // Guardar el contenido original
    const originalContent = element.innerHTML;
    
    // Mostrar indicador de carga
    element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cargando...';
    element.disabled = true;
    
    // Simular tiempo de carga
    setTimeout(() => {
        // Restaurar contenido
        element.innerHTML = originalContent;
        element.disabled = false;
        
        // Ejecutar callback
        if (typeof callback === 'function') {
            callback();
        }
    }, time);
}

// Función para obtener parámetros de URL
function getUrlParams() {
    const params = {};
    const queryString = window.location.search.substring(1);
    const pairs = queryString.split('&');
    
    for (let i = 0; i < pairs.length; i++) {
        const pair = pairs[i].split('=');
        params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
    }
    
    return params;
}

// Función para formatear fechas
function formatDate(date) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(date).toLocaleDateString('es-ES', options);
}

// Función para generar ID únicos
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// Función para guardar datos en localStorage
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('Error al guardar en localStorage:', error);
        return false;
    }
}

// Función para obtener datos de localStorage
function getFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('Error al obtener de localStorage:', error);
        return null;
    }
}

// Función para eliminar datos de localStorage
function removeFromLocalStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('Error al eliminar de localStorage:', error);
        return false;
    }
}

// Exportar funciones para uso en otros archivos
window.MusicApp = {
    showAlert,
    validateForm,
    simulateLoading,
    getUrlParams,
    formatDate,
    generateId,
    saveToLocalStorage,
    getFromLocalStorage,
    removeFromLocalStorage
};
