// JavaScript para la página de escalas con API de Tonal.js - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Elementos de la interfaz
    const instrumentButtons = document.querySelectorAll('.btn-instrument');
    const scalePositionsText = document.querySelector('.scale-positions-text');
    const rootNoteSelect = document.getElementById('root-note');
    const scaleTypeSelect = document.getElementById('scale-type');
    const scaleNameElement = document.querySelector('.scale-name');
    const guitarPositions = document.querySelector('.guitar-positions');
    const pianoPositions = document.querySelector('.piano-positions');
    const bassPositions = document.querySelector('.bass-positions');

    // Verificar que el servicio de escalas esté disponible
    if (!window.ScalesApiService) {
        console.error('ScalesApiService no está disponible');
        return;
    }

    // Datos simplificados de escalas
    const scaleData = {
        scales: {
            major: {
                name: 'Mayor',
                formula: 'T - T - S - T - T - T - S',
                description: 'Escala mayor básica, sonido alegre y estable'
            },
            minor: {
                name: 'Menor',
                formula: 'T - S - T - T - S - T - T',
                description: 'Escala menor natural, sonido melancólico'
            },
            dorian: {
                name: 'Dórico',
                formula: 'T - S - T - T - T - S - T',
                description: 'Modo menor con sexta mayor, sonido jazzy'
            }
        },

        // Posiciones simplificadas por instrumento
        positions: {
            guitar: {
                C: {
                    major: [
                        'Cuerda 6 (Mi): Traste 3 (G), Traste 5 (A)',
                        'Cuerda 5 (La): Traste 2 (B), Traste 3 (C), Traste 5 (D)',
                        'Cuerda 4 (Re): Traste 2 (E), Traste 4 (F#), Traste 5 (G)',
                        'Cuerda 3 (Sol): Traste 2 (A), Traste 4 (B)',
                        'Cuerda 2 (Si): Traste 1 (C), Traste 3 (D), Traste 5 (E)',
                        'Cuerda 1 (Mi): Traste 3 (G), Traste 5 (A)'
                    ]
                }
            },
            piano: {
                C: {
                    major: [
                        'Teclas blancas: C - D - E - F - G - A - B - C',
                        'Digitación mano derecha: 1-2-3-1-2-3-4-5',
                        'Digitación mano izquierda: 5-4-3-2-1-3-2-1',
                        'Patrón: Solo teclas blancas, sin sostenidos ni bemoles'
                    ]
                }
            },
            bass: {
                C: {
                    major: [
                        'Cuerda 4 (Mi): Traste 3 (G), Traste 5 (A)',
                        'Cuerda 3 (La): Traste 2 (B), Traste 3 (C), Traste 5 (D)',
                        'Cuerda 2 (Re): Traste 2 (E), Traste 4 (F#), Traste 5 (G)',
                        'Cuerda 1 (Sol): Traste 2 (A), Traste 4 (B), Traste 5 (C)'
                    ]
                }
            }
        }
    };

    // Estado actual
    let currentInstrument = 'guitar';
    let currentRoot = 'C';
    let currentType = 'major';

    // Función para cambiar el instrumento
    function changeInstrument(instrument) {
        currentInstrument = instrument;

        // Actualizar botones
        instrumentButtons.forEach(button => {
            button.classList.remove('active');
            if (button.getAttribute('data-instrument') === instrument) {
                button.classList.add('active');
            }
        });

        // Actualizar posiciones mostradas
        updateInstrumentPositions(instrument);

        // Actualizar la escala mostrada
        updateScaleDisplay();
    }

    // Función para actualizar las posiciones del instrumento
    function updateInstrumentPositions(instrument) {
        // Ocultar todas las posiciones
        if (guitarPositions) guitarPositions.classList.remove('active');
        if (pianoPositions) pianoPositions.classList.remove('active');
        if (bassPositions) bassPositions.classList.remove('active');

        // Mostrar la posición del instrumento seleccionado
        switch(instrument) {
            case 'guitar':
                if (guitarPositions) guitarPositions.classList.add('active');
                break;
            case 'piano':
                if (pianoPositions) pianoPositions.classList.add('active');
                break;
            case 'bass':
                if (bassPositions) bassPositions.classList.add('active');
                break;
        }
    }

    // Función para actualizar la visualización de la escala
    async function updateScaleDisplay() {
        // Mostrar indicador de carga
        showLoadingIndicator();

        try {
            // Obtener escala desde la API
            const response = await window.ScalesApiService.getScaleForInstrument(
                currentRoot,
                currentType,
                currentInstrument
            );

            if (response.success) {
                const scaleData = response.data;

                // Actualizar el nombre de la escala
                if (scaleNameElement) {
                    scaleNameElement.textContent = scaleData.scale.name;
                }

                // Actualizar las posiciones de la escala
                updateScalePositions(scaleData);

                console.log(`Escala cargada desde: ${response.source}`);
            } else {
                console.error('Error al cargar escala:', response.error);
                showErrorMessage(response.error);
            }
        } catch (error) {
            console.error('Error al actualizar escala:', error);
            showErrorMessage('Error al cargar la escala');
        } finally {
            hideLoadingIndicator();
        }
    }

    // Mostrar indicador de carga
    function showLoadingIndicator() {
        if (scaleNameElement) {
            scaleNameElement.textContent = 'Cargando escala...';
        }
    }

    // Ocultar indicador de carga
    function hideLoadingIndicator() {
        // Se actualiza automáticamente cuando se carga la escala
    }

    // Mostrar mensaje de error
    function showErrorMessage(message) {
        if (scaleNameElement) {
            scaleNameElement.textContent = `Error: ${message}`;
        }
    }

    // Función para actualizar las posiciones de la escala
    function updateScalePositions(scaleData) {
        // Actualizar las posiciones según el instrumento
        switch(currentInstrument) {
            case 'guitar':
                updateGuitarScalePositions(scaleData);
                break;
            case 'piano':
                updatePianoScalePositions(scaleData);
                break;
            case 'bass':
                updateBassScalePositions(scaleData);
                break;
        }
    }

    // Función para actualizar posiciones de guitarra
    function updateGuitarScalePositions(scaleData) {
        if (!guitarPositions) return;

        const positionDetails = guitarPositions.querySelector('.position-details');
        if (positionDetails && scaleData.positions) {
            positionDetails.innerHTML = '';

            // Mostrar información de la escala
            const scaleInfo = document.createElement('div');
            scaleInfo.innerHTML = `
                <p><strong>Notas de la escala:</strong> ${scaleData.scale.notes.join(' - ')}</p>
                <p><strong>Nota raíz:</strong> ${scaleData.scale.root}</p>
            `;
            positionDetails.appendChild(scaleInfo);

            // Mostrar posiciones
            scaleData.positions.forEach(position => {
                const positionDiv = document.createElement('div');
                positionDiv.className = 'scale-position';
                positionDiv.innerHTML = `<h5>${position.name}</h5>`;

                position.strings.forEach(string => {
                    if (string.positions.length > 0) {
                        const stringInfo = string.positions.map(pos =>
                            `Traste ${pos.fret} (${pos.note}${pos.isRoot ? ' - RAÍZ' : ''})`
                        ).join(', ');

                        const p = document.createElement('p');
                        p.innerHTML = `<strong>Cuerda ${string.string} (${string.openNote}):</strong> ${stringInfo}`;
                        positionDiv.appendChild(p);
                    }
                });

                positionDetails.appendChild(positionDiv);
            });
        }
    }

    // Función para actualizar posiciones de piano
    function updatePianoScalePositions(scaleData) {
        if (!pianoPositions) return;

        const positionDetails = pianoPositions.querySelector('.position-details');
        if (positionDetails && scaleData.positions) {
            positionDetails.innerHTML = '';

            // Mostrar información de la escala
            const scaleInfo = document.createElement('div');
            scaleInfo.innerHTML = `
                <p><strong>Notas de la escala:</strong> ${scaleData.scale.notes.join(' - ')}</p>
                <p><strong>Nota raíz:</strong> ${scaleData.scale.root}</p>
            `;
            positionDetails.appendChild(scaleInfo);

            // Mostrar posiciones
            scaleData.positions.forEach(position => {
                const positionDiv = document.createElement('div');
                positionDiv.className = 'scale-position';
                positionDiv.innerHTML = `
                    <h5>${position.name}</h5>
                    <p><strong>Mano derecha:</strong> ${position.rightHand.notes.join(' - ')}</p>
                    <p><strong>Digitación MD:</strong> ${position.rightHand.fingering}</p>
                    <p><strong>Mano izquierda:</strong> ${position.leftHand.notes.join(' - ')}</p>
                    <p><strong>Digitación MI:</strong> ${position.leftHand.fingering}</p>
                `;
                positionDetails.appendChild(positionDiv);
            });
        }
    }

    // Función para actualizar posiciones de bajo
    function updateBassScalePositions(scaleData) {
        if (!bassPositions) return;

        const positionDetails = bassPositions.querySelector('.position-details');
        if (positionDetails && scaleData.positions) {
            positionDetails.innerHTML = '';

            // Mostrar información de la escala
            const scaleInfo = document.createElement('div');
            scaleInfo.innerHTML = `
                <p><strong>Notas de la escala:</strong> ${scaleData.scale.notes.join(' - ')}</p>
                <p><strong>Nota raíz:</strong> ${scaleData.scale.root}</p>
            `;
            positionDetails.appendChild(scaleInfo);

            // Mostrar posiciones
            scaleData.positions.forEach(position => {
                const positionDiv = document.createElement('div');
                positionDiv.className = 'scale-position';
                positionDiv.innerHTML = `<h5>${position.name}</h5>`;

                position.strings.forEach(string => {
                    if (string.positions.length > 0) {
                        const stringInfo = string.positions.map(pos =>
                            `Traste ${pos.fret} (${pos.note}${pos.isRoot ? ' - RAÍZ' : ''})`
                        ).join(', ');

                        const p = document.createElement('p');
                        p.innerHTML = `<strong>Cuerda ${string.string} (${string.openNote}):</strong> ${stringInfo}`;
                        positionDiv.appendChild(p);
                    }
                });

                positionDetails.appendChild(positionDiv);
            });
        }
    }

    // Event listeners simplificados

    // Event listeners para los botones de instrumento
    instrumentButtons.forEach(button => {
        button.addEventListener('click', function() {
            const instrument = this.getAttribute('data-instrument');
            changeInstrument(instrument);
        });
    });

    // Event listeners para los selectores de escala
    if (rootNoteSelect) {
        rootNoteSelect.addEventListener('change', function() {
            currentRoot = this.value;
            updateScaleDisplay();
        });
    }

    if (scaleTypeSelect) {
        scaleTypeSelect.addEventListener('change', function() {
            currentType = this.value;
            updateScaleDisplay();
        });
    }

    // Inicializar la visualización
    updateScaleDisplay();
});
