// JavaScript simplificado para la página de escalas - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Elementos de la interfaz
    const instrumentButtons = document.querySelectorAll('.btn-instrument');
    const scalePositionsText = document.querySelector('.scale-positions-text');
    const rootNoteSelect = document.getElementById('root-note');
    const scaleTypeSelect = document.getElementById('scale-type');
    const scaleNameElement = document.querySelector('.scale-name');
    const guitarPositions = document.querySelector('.guitar-positions');
    const pianoPositions = document.querySelector('.piano-positions');
    const bassPositions = document.querySelector('.bass-positions');

    // Datos simplificados de escalas
    const scaleData = {
        scales: {
            major: {
                name: 'Mayor',
                formula: 'T - T - S - T - T - T - S',
                description: 'Escala mayor b<PERSON><PERSON>, sonido alegre y estable'
            },
            minor: {
                name: '<PERSON><PERSON>',
                formula: 'T - S - T - T - S - T - T',
                description: 'Escala menor natural, sonido melancólico'
            },
            dorian: {
                name: 'Dórico',
                formula: 'T - S - T - T - T - S - T',
                description: 'Modo menor con sexta mayor, sonido jazzy'
            }
        },

        // Posiciones simplificadas por instrumento
        positions: {
            guitar: {
                C: {
                    major: [
                        'Cuerda 6 (Mi): Traste 3 (G), Traste 5 (A)',
                        'Cuerda 5 (La): Traste 2 (B), Traste 3 (C), Traste 5 (D)',
                        'Cuerda 4 (Re): Traste 2 (E), Traste 4 (F#), Traste 5 (G)',
                        'Cuerda 3 (Sol): Traste 2 (A), Traste 4 (B)',
                        'Cuerda 2 (Si): Traste 1 (C), Traste 3 (D), Traste 5 (E)',
                        'Cuerda 1 (Mi): Traste 3 (G), Traste 5 (A)'
                    ]
                }
            },
            piano: {
                C: {
                    major: [
                        'Teclas blancas: C - D - E - F - G - A - B - C',
                        'Digitación mano derecha: 1-2-3-1-2-3-4-5',
                        'Digitación mano izquierda: 5-4-3-2-1-3-2-1',
                        'Patrón: Solo teclas blancas, sin sostenidos ni bemoles'
                    ]
                }
            },
            bass: {
                C: {
                    major: [
                        'Cuerda 4 (Mi): Traste 3 (G), Traste 5 (A)',
                        'Cuerda 3 (La): Traste 2 (B), Traste 3 (C), Traste 5 (D)',
                        'Cuerda 2 (Re): Traste 2 (E), Traste 4 (F#), Traste 5 (G)',
                        'Cuerda 1 (Sol): Traste 2 (A), Traste 4 (B), Traste 5 (C)'
                    ]
                }
            }
        }
    };

    // Estado actual
    let currentInstrument = 'guitar';
    let currentRoot = 'C';
    let currentType = 'major';

    // Función para cambiar el instrumento
    function changeInstrument(instrument) {
        currentInstrument = instrument;

        // Actualizar botones
        instrumentButtons.forEach(button => {
            button.classList.remove('active');
            if (button.getAttribute('data-instrument') === instrument) {
                button.classList.add('active');
            }
        });

        // Actualizar posiciones mostradas
        updateInstrumentPositions(instrument);

        // Actualizar la escala mostrada
        updateScaleDisplay();
    }

    // Función para actualizar las posiciones del instrumento
    function updateInstrumentPositions(instrument) {
        // Ocultar todas las posiciones
        if (guitarPositions) guitarPositions.classList.remove('active');
        if (pianoPositions) pianoPositions.classList.remove('active');
        if (bassPositions) bassPositions.classList.remove('active');

        // Mostrar la posición del instrumento seleccionado
        switch(instrument) {
            case 'guitar':
                if (guitarPositions) guitarPositions.classList.add('active');
                break;
            case 'piano':
                if (pianoPositions) pianoPositions.classList.add('active');
                break;
            case 'bass':
                if (bassPositions) bassPositions.classList.add('active');
                break;
        }
    }

    // Función para actualizar la visualización de la escala
    function updateScaleDisplay() {
        // Actualizar el nombre de la escala
        if (scaleNameElement) {
            const scaleName = scaleData.scales[currentType]?.name || currentType;
            scaleNameElement.textContent = `${currentRoot} ${scaleName}`;
        }

        // Actualizar las posiciones de la escala
        updateScalePositions();
    }

    // Función para actualizar las posiciones de la escala
    function updateScalePositions() {
        const positions = getScalePositions(currentInstrument, currentRoot, currentType);

        // Actualizar las posiciones según el instrumento
        switch(currentInstrument) {
            case 'guitar':
                updateGuitarScalePositions(positions);
                break;
            case 'piano':
                updatePianoScalePositions(positions);
                break;
            case 'bass':
                updateBassScalePositions(positions);
                break;
        }
    }

    // Función para obtener las posiciones de una escala
    function getScalePositions(instrument, root, type) {
        // Obtener posiciones o usar genéricas
        if (scaleData.positions[instrument] &&
            scaleData.positions[instrument][root] &&
            scaleData.positions[instrument][root][type]) {
            return scaleData.positions[instrument][root][type];
        }

        // Posiciones genéricas
        return [`Posiciones para escala ${root} ${type} en ${instrument} no disponibles`];
    }

    // Función para actualizar posiciones de guitarra
    function updateGuitarScalePositions(positions) {
        if (!guitarPositions) return;

        const positionDetails = guitarPositions.querySelector('.position-details');
        if (positionDetails) {
            positionDetails.innerHTML = '';
            positions.forEach(position => {
                const p = document.createElement('p');
                p.innerHTML = `<strong>${position.split(':')[0]}:</strong>${position.split(':')[1] || ''}`;
                positionDetails.appendChild(p);
            });
        }
    }

    // Función para actualizar posiciones de piano
    function updatePianoScalePositions(positions) {
        if (!pianoPositions) return;

        const positionDetails = pianoPositions.querySelector('.position-details');
        if (positionDetails) {
            positionDetails.innerHTML = '';
            positions.forEach(position => {
                const p = document.createElement('p');
                p.innerHTML = `<strong>${position.split(':')[0]}:</strong>${position.split(':')[1] || ''}`;
                positionDetails.appendChild(p);
            });
        }
    }

    // Función para actualizar posiciones de bajo
    function updateBassScalePositions(positions) {
        if (!bassPositions) return;

        const positionDetails = bassPositions.querySelector('.position-details');
        if (positionDetails) {
            positionDetails.innerHTML = '';
            positions.forEach(position => {
                const p = document.createElement('p');
                p.innerHTML = `<strong>${position.split(':')[0]}:</strong>${position.split(':')[1] || ''}`;
                positionDetails.appendChild(p);
            });
        }
    }

    // Event listeners simplificados

    // Event listeners para los botones de instrumento
    instrumentButtons.forEach(button => {
        button.addEventListener('click', function() {
            const instrument = this.getAttribute('data-instrument');
            changeInstrument(instrument);
        });
    });

    // Event listeners para los selectores de escala
    if (rootNoteSelect) {
        rootNoteSelect.addEventListener('change', function() {
            currentRoot = this.value;
            updateScaleDisplay();
        });
    }

    if (scaleTypeSelect) {
        scaleTypeSelect.addEventListener('change', function() {
            currentType = this.value;
            updateScaleDisplay();
        });
    }

    // Inicializar la visualización
    updateScaleDisplay();
});
