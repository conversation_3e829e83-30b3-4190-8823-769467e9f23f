// JavaScript para la página de escalas - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Elementos de la interfaz
    const instrumentButtons = document.querySelectorAll('.btn-instrument');
    const scaleDiagrams = document.querySelectorAll('.scale-diagram');
    const rootNoteSelect = document.getElementById('root-note');
    const scaleTypeSelect = document.getElementById('scale-type');
    const scaleNameElement = document.querySelector('.scale-name');
    const positionButtons = document.querySelectorAll('.position-btn');
    const relatedScales = document.querySelectorAll('.related-scale');

    // Datos de escalas con todos los modos musicales
    const scaleData = {
        // Definición de escalas y modos
        scales: {
            major: {
                intervals: [0, 2, 4, 5, 7, 9, 11],
                formula: 'T - T - S - T - T - T - S',
                name: 'Mayor (Jónico)',
                description: 'La escala mayor es la escala fundamental en la música occidental, con un sonido brillante y estable.',
                chords: ['I', 'ii', 'iii', 'IV', 'V', 'vi', 'vii°']
            },
            minor: {
                intervals: [0, 2, 3, 5, 7, 8, 10],
                formula: 'T - S - T - T - S - T - T',
                name: 'Menor natural (Eólico)',
                description: 'La escala menor natural tiene un sonido más oscuro y melancólico que la escala mayor.',
                chords: ['i', 'ii°', 'bIII', 'iv', 'v', 'bVI', 'bVII']
            },
            dorian: {
                intervals: [0, 2, 3, 5, 7, 9, 10],
                formula: 'T - S - T - T - T - S - T',
                name: 'Dórico',
                description: 'El modo dórico tiene un carácter menor pero con un sexto grado mayor que le da un sonido más brillante.',
                chords: ['i', 'ii', 'bIII', 'IV', 'v°', 'vi', 'bVII']
            },
            phrygian: {
                intervals: [0, 1, 3, 5, 7, 8, 10],
                formula: 'S - T - T - T - S - T - T',
                name: 'Frigio',
                description: 'El modo frigio tiene un carácter menor muy oscuro y exótico, usado frecuentemente en música flamenca.',
                chords: ['i', 'bII', 'biii°', 'iv', 'bV', 'bvi', 'bvii']
            },
            lydian: {
                intervals: [0, 2, 4, 6, 7, 9, 11],
                formula: 'T - T - T - S - T - T - S',
                name: 'Lidio',
                description: 'El modo lidio tiene un carácter mayor muy brillante debido a su cuarto grado aumentado.',
                chords: ['I', 'II', 'iii°', '#iv', 'V', 'vi', 'vii']
            },
            mixolydian: {
                intervals: [0, 2, 4, 5, 7, 9, 10],
                formula: 'T - T - S - T - T - S - T',
                name: 'Mixolidio',
                description: 'El modo mixolidio tiene un carácter mayor pero con séptimo menor, muy usado en blues y rock.',
                chords: ['I', 'ii', 'iii°', 'IV', 'v', 'vi', 'bVII']
            },
            aeolian: {
                intervals: [0, 2, 3, 5, 7, 8, 10],
                formula: 'T - S - T - T - S - T - T',
                name: 'Eólico (Menor Natural)',
                description: 'El modo eólico es equivalente a la escala menor natural. Es el modo menor más común.',
                chords: ['i', 'ii°', 'bIII', 'iv', 'v', 'bVI', 'bVII']
            },
            locrian: {
                intervals: [0, 1, 3, 5, 6, 8, 10],
                formula: 'S - T - T - S - T - T - T',
                name: 'Locrio',
                description: 'El modo locrio es muy inestable debido a su quinta disminuida, raramente usado como centro tonal.',
                chords: ['i°', 'bII', 'biii', 'iv', 'bV', 'bVI', 'bvii']
            },
            pentatonic_major: {
                intervals: [0, 2, 4, 7, 9],
                formula: 'T - T - T½ - T - T½',
                name: 'Pentatónica mayor',
                description: 'La escala pentatónica mayor es una escala de cinco notas muy usada en música folk, country y rock.',
                chords: ['I', 'ii', 'iii', 'V', 'vi']
            },
            pentatonic_minor: {
                intervals: [0, 3, 5, 7, 10],
                formula: 'T½ - T - T - T½ - T',
                name: 'Pentatónica menor',
                description: 'La escala pentatónica menor es fundamental en blues, rock y música popular.',
                chords: ['i', 'bIII', 'iv', 'v', 'bVII']
            },
            blues: {
                intervals: [0, 3, 5, 6, 7, 10],
                formula: 'T½ - T - S - S - T½ - T',
                name: 'Blues',
                description: 'La escala de blues añade la "blue note" (quinta disminuida) a la pentatónica menor.',
                chords: ['i', 'bIII', 'iv', 'bV', 'v', 'bVII']
            }
        },

        // Notas musicales
        notes: ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'],

        // Relaciones entre escalas y modos
        related: {
            'C_major': ['C_dorian', 'C_phrygian', 'C_lydian', 'C_mixolydian', 'A_minor', 'C_pentatonic_major'],
            'C_dorian': ['C_major', 'C_minor', 'C_phrygian', 'C_aeolian', 'Bb_major', 'C_pentatonic_minor'],
            'C_phrygian': ['C_dorian', 'C_minor', 'C_locrian', 'Ab_major', 'C_pentatonic_minor', 'C_blues'],
            'C_lydian': ['C_major', 'C_mixolydian', 'G_major', 'C_pentatonic_major'],
            'C_mixolydian': ['C_major', 'C_lydian', 'F_major', 'C_pentatonic_major', 'C_blues'],
            'C_aeolian': ['C_minor', 'C_dorian', 'C_phrygian', 'Eb_major', 'C_pentatonic_minor'],
            'C_locrian': ['C_phrygian', 'C_minor', 'Db_major'],
            'A_minor': ['A_dorian', 'A_phrygian', 'A_aeolian', 'C_major', 'A_pentatonic_minor', 'A_blues'],
            'C_pentatonic_major': ['C_major', 'C_lydian', 'C_mixolydian', 'A_pentatonic_minor'],
            'C_pentatonic_minor': ['C_minor', 'C_dorian', 'C_phrygian', 'C_blues', 'A_pentatonic_major'],
            'C_blues': ['C_pentatonic_minor', 'C_minor', 'C_phrygian', 'C_mixolydian']
        },

        // Posiciones en el diapasón para guitarra
        guitarPositions: {
            major: [
                { startFret: 0, pattern: 'Posición 1' },
                { startFret: 2, pattern: 'Posición 2' },
                { startFret: 4, pattern: 'Posición 3' },
                { startFret: 5, pattern: 'Posición 4' },
                { startFret: 7, pattern: 'Posición 5' },
                { startFret: 9, pattern: 'Posición 6' },
                { startFret: 11, pattern: 'Posición 7' }
            ]
        }
    };

    // Estado actual
    let currentInstrument = 'guitar';
    let currentRoot = 'C';
    let currentType = 'major';
    let currentPosition = 1;

    // Función para cambiar el instrumento
    function changeInstrument(instrument) {
        currentInstrument = instrument;

        // Actualizar botones
        instrumentButtons.forEach(button => {
            button.classList.remove('active');
            if (button.getAttribute('data-instrument') === instrument) {
                button.classList.add('active');
            }
        });

        // Actualizar diagramas
        scaleDiagrams.forEach(diagram => {
            diagram.classList.remove('active');
            if (diagram.classList.contains(`${instrument}-diagram`)) {
                diagram.classList.add('active');
            }
        });

        // Actualizar la escala mostrada
        updateScaleDisplay();
    }

    // Función para actualizar la visualización de la escala
    function updateScaleDisplay() {
        // Actualizar el nombre de la escala
        if (scaleNameElement) {
            const scaleName = scaleData.scales[currentType]?.name || currentType;
            scaleNameElement.textContent = `${currentRoot} ${scaleName}`;
        }

        // Generar las notas de la escala
        const scaleNotes = generateScaleNotes(currentRoot, currentType);

        // Actualizar la lista de notas
        updateNotesList(scaleNotes);

        // Actualizar la fórmula
        updateScaleFormula(currentType);

        // Actualizar el diagrama según el instrumento
        updateScaleDiagram(scaleNotes);

        // Actualizar posiciones
        updatePositions();

        // Actualizar escalas relacionadas
        updateRelatedScales();
    }

    // Función para generar las notas de una escala
    function generateScaleNotes(root, type) {
        // Obtener el índice de la nota raíz
        const rootIndex = scaleData.notes.indexOf(root);
        if (rootIndex === -1 || !scaleData.scales[type]) return [];

        // Obtener los intervalos de la escala
        const intervals = scaleData.scales[type].intervals;

        // Generar las notas
        return intervals.map(interval => {
            const noteIndex = (rootIndex + interval) % 12;
            return scaleData.notes[noteIndex];
        });
    }

    // Función para actualizar la lista de notas
    function updateNotesList(notes) {
        const notesContainer = document.querySelector('.notes-container');
        if (!notesContainer) return;

        // Limpiar notas anteriores
        notesContainer.innerHTML = '';

        // Añadir nuevas notas
        notes.forEach((note, index) => {
            const noteElement = document.createElement('span');
            noteElement.className = 'scale-note';
            noteElement.textContent = note;

            // Marcar la nota raíz
            if (index === 0) {
                noteElement.classList.add('root');
            }

            notesContainer.appendChild(noteElement);
        });
    }

    // Función para actualizar la fórmula de la escala
    function updateScaleFormula(type) {
        const formulaElement = document.querySelector('.scale-formula p');
        if (!formulaElement) return;

        const formula = scaleData.scales[type]?.formula || '';
        formulaElement.textContent = formula;
    }

    // Función para actualizar el diagrama de la escala
    function updateScaleDiagram(notes) {
        if (currentInstrument === 'guitar') {
            updateGuitarDiagram(notes);
        } else if (currentInstrument === 'piano') {
            updatePianoDiagram(notes);
        } else if (currentInstrument === 'bass') {
            updateBassDiagram(notes);
        }
    }

    // Función para actualizar el diagrama de guitarra
    function updateGuitarDiagram(notes) {
        const guitarDiagram = document.querySelector('.guitar-diagram');
        if (!guitarDiagram) return;

        // Limpiar notas anteriores
        const scaleNotes = guitarDiagram.querySelector('.scale-notes');
        if (scaleNotes) {
            scaleNotes.innerHTML = '';
        }

        // Definir las notas de las cuerdas al aire
        const openStrings = ['E', 'B', 'G', 'D', 'A', 'E'];

        // Obtener la posición actual
        const position = scaleData.guitarPositions.major[currentPosition - 1] || { startFret: 0 };
        const startFret = position.startFret;

        // Para cada cuerda y cada traste, verificar si la nota está en la escala
        for (let string = 0; string < 6; string++) {
            const openNote = openStrings[string];
            const openNoteIndex = scaleData.notes.indexOf(openNote);

            for (let fret = startFret; fret < startFret + 12; fret++) {
                const noteIndex = (openNoteIndex + fret) % 12;
                const note = scaleData.notes[noteIndex];

                // Si la nota está en la escala, mostrarla
                if (notes.includes(note)) {
                    const noteElement = document.createElement('div');
                    noteElement.className = 'scale-note';

                    // Calcular posición
                    const stringPosition = string * 20; // 0%, 20%, 40%, 60%, 80%, 100%
                    const fretPosition = ((fret - startFret) * 8.33) + 4.165; // Distribuir en 12 trastes

                    noteElement.style.top = `${stringPosition}%`;
                    noteElement.style.left = `${fretPosition}%`;

                    // Marcar la nota raíz
                    if (note === currentRoot) {
                        noteElement.classList.add('root');
                    }

                    // Mostrar la nota
                    noteElement.textContent = note;

                    scaleNotes.appendChild(noteElement);
                }
            }
        }
    }

    // Función para actualizar el diagrama de piano
    function updatePianoDiagram(notes) {
        const pianoDiagram = document.querySelector('.piano-diagram');
        if (!pianoDiagram) return;

        // Resetear teclas
        const pianoKeys = pianoDiagram.querySelectorAll('.piano-key');
        pianoKeys.forEach(key => {
            key.classList.remove('active');
            key.classList.remove('root');
        });

        // Activar teclas de la escala
        notes.forEach(note => {
            const keys = pianoDiagram.querySelectorAll(`.piano-key[data-note="${note}"]`);
            keys.forEach(key => {
                key.classList.add('active');

                // Marcar la nota raíz
                if (note === currentRoot) {
                    key.classList.add('root');
                }
            });
        });
    }

    // Función para actualizar el diagrama de bajo
    function updateBassDiagram(notes) {
        const bassDiagram = document.querySelector('.bass-diagram');
        if (!bassDiagram) return;

        // Limpiar notas anteriores
        const scaleNotes = bassDiagram.querySelector('.scale-notes');
        if (scaleNotes) {
            scaleNotes.innerHTML = '';
        }

        // Definir las notas de las cuerdas al aire
        const openStrings = ['G', 'D', 'A', 'E'];

        // Obtener la posición actual
        const position = scaleData.guitarPositions.major[currentPosition - 1] || { startFret: 0 };
        const startFret = position.startFret;

        // Para cada cuerda y cada traste, verificar si la nota está en la escala
        for (let string = 0; string < 4; string++) {
            const openNote = openStrings[string];
            const openNoteIndex = scaleData.notes.indexOf(openNote);

            for (let fret = startFret; fret < startFret + 12; fret++) {
                const noteIndex = (openNoteIndex + fret) % 12;
                const note = scaleData.notes[noteIndex];

                // Si la nota está en la escala, mostrarla
                if (notes.includes(note)) {
                    const noteElement = document.createElement('div');
                    noteElement.className = 'scale-note';

                    // Calcular posición
                    const stringPosition = string * 33.33; // 0%, 33.33%, 66.66%, 100%
                    const fretPosition = ((fret - startFret) * 8.33) + 4.165; // Distribuir en 12 trastes

                    noteElement.style.top = `${stringPosition}%`;
                    noteElement.style.left = `${fretPosition}%`;

                    // Marcar la nota raíz
                    if (note === currentRoot) {
                        noteElement.classList.add('root');
                    }

                    // Mostrar la nota
                    noteElement.textContent = note;

                    scaleNotes.appendChild(noteElement);
                }
            }
        }
    }

    // Función para actualizar las posiciones
    function updatePositions() {
        // Actualizar botones de posición
        positionButtons.forEach(button => {
            button.classList.remove('active');
            if (parseInt(button.getAttribute('data-position')) === currentPosition) {
                button.classList.add('active');
            }
        });
    }

    // Función para actualizar las escalas relacionadas
    function updateRelatedScales() {
        const relatedGrid = document.querySelector('.related-scales-grid');
        if (!relatedGrid) return;

        // Verificar si tenemos datos para esta escala
        const scaleKey = `${currentRoot}_${currentType}`;
        if (!scaleData.related[scaleKey]) {
            relatedGrid.innerHTML = '<p>No hay escalas relacionadas disponibles.</p>';
            return;
        }

        const related = scaleData.related[scaleKey];

        // Limpiar escalas relacionadas anteriores
        relatedGrid.innerHTML = '';

        // Añadir nuevas escalas relacionadas
        related.forEach(scale => {
            const [root, type] = scale.split('_');
            const scaleName = scaleData.scales[type]?.name || type;

            const link = document.createElement('a');
            link.className = 'related-scale';
            link.setAttribute('data-scale', scale);
            link.textContent = `${root} ${scaleName}`;
            link.href = '#';

            link.addEventListener('click', function(e) {
                e.preventDefault();
                // Aquí se podría implementar la lógica para cambiar a la escala relacionada
                MusicApp.showAlert(`Cambiando a la escala ${root} ${scaleName}`, 'info');
            });

            relatedGrid.appendChild(link);
        });
    }

    // Event listeners para los botones de instrumento
    instrumentButtons.forEach(button => {
        button.addEventListener('click', function() {
            const instrument = this.getAttribute('data-instrument');
            changeInstrument(instrument);
        });
    });

    // Event listeners para los selectores de escala
    if (rootNoteSelect) {
        rootNoteSelect.addEventListener('change', function() {
            currentRoot = this.value;
            updateScaleDisplay();
        });
    }

    if (scaleTypeSelect) {
        scaleTypeSelect.addEventListener('change', function() {
            currentType = this.value;
            updateScaleDisplay();
        });
    }

    // Event listeners para los botones de posición
    positionButtons.forEach(button => {
        button.addEventListener('click', function() {
            currentPosition = parseInt(this.getAttribute('data-position'));
            updatePositions();

            // Actualizar el diagrama con la nueva posición
            const scaleNotes = generateScaleNotes(currentRoot, currentType);
            updateScaleDiagram(scaleNotes);
        });
    });

    // Inicializar la visualización
    updateScaleDisplay();
});
