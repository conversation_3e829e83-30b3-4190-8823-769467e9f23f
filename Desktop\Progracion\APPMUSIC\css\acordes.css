/* Estilos para la página de acordes - MusicApp */

.acordes-container {
    padding: var(--spacing-xl) 0;
}

.instrument-selector {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.btn-instrument {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    background-color: var(--color-background-light);
    color: var(--color-text);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    border: 1px solid var(--color-border);
}

.btn-instrument i {
    margin-right: var(--spacing-sm);
}

.btn-instrument:hover {
    background-color: var(--color-background-lighter);
}

.btn-instrument.active {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

/* Estilos para la búsqueda de acordes */
.chord-search {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.search-bar {
    display: flex;
    max-width: 500px;
    width: 100%;
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: 1px solid var(--color-border);
}

.search-bar input {
    flex: 1;
    padding: var(--spacing-md);
    background-color: transparent;
    border: none;
    color: var(--color-text);
    font-size: var(--font-size-md);
}

.search-bar input:focus {
    outline: none;
}

.search-bar button {
    padding: 0 var(--spacing-lg);
    background-color: var(--color-primary);
    color: var(--color-text);
    border: none;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-bar button:hover {
    background-color: var(--color-primary-dark);
}

/* Modal de resultados de búsqueda */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
}

.modal-header h3 {
    margin: 0;
    color: var(--color-primary);
}

.close-modal {
    font-size: var(--font-size-xl);
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.close-modal:hover {
    color: var(--color-text);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: calc(80vh - 70px);
}

#search-results-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.search-result-card {
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid var(--color-border);
}

.search-result-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--color-primary);
}

.search-result-card h4 {
    margin-top: 0;
    margin-bottom: var(--spacing-sm);
    color: var(--color-primary);
}

.search-result-card p {
    margin: 0;
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
}

.search-result-image {
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    background-color: var(--color-background-dark);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.search-result-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Estilos para la sección de favoritos */
.favorites-section {
    margin-bottom: var(--spacing-xl);
}

.favorites-section h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--color-primary);
    text-align: center;
}

.favorites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.favorite-card {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    cursor: pointer;
    position: relative;
}

.favorite-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.favorite-image {
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--color-background);
}

.favorite-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.favorite-info {
    padding: var(--spacing-sm);
    text-align: center;
}

.favorite-name {
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-xs);
}

.favorite-instrument {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.remove-favorite {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.favorite-card:hover .remove-favorite {
    opacity: 1;
}

.no-favorites {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--color-text-secondary);
}

/* Estilos para la sección de teoría musical */
.theory-section {
    margin-bottom: var(--spacing-xl);
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.theory-section h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
    color: var(--color-primary);
    text-align: center;
}

.theory-tabs {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.theory-tab {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    color: var(--color-text);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.theory-tab:hover {
    background-color: var(--color-background-lighter);
}

.theory-tab.active {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: white;
}

.theory-tab-content {
    display: none;
}

.theory-tab-content.active {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.theory-card {
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-xs);
}

.theory-card h4 {
    color: var(--color-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
}

.theory-card p, .theory-card ul {
    color: var(--color-text);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.theory-card ul {
    padding-left: var(--spacing-lg);
}

.theory-card li {
    margin-bottom: var(--spacing-xs);
}

.chord-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.chord-selector {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.chord-selector label {
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
}

.chord-select {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    background-color: var(--color-background);
    color: var(--color-text);
    border: 1px solid var(--color-border);
    min-width: 150px;
}

.chord-select:focus {
    outline: none;
    border-color: var(--color-primary);
}

.chord-display {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.chord-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--spacing-md);
    position: relative;
}

.chord-name {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    text-align: center;
    color: var(--color-primary);
}

.favorite-btn {
    position: absolute;
    right: 0;
    background: none;
    border: none;
    color: var(--color-text-secondary);
    font-size: var(--font-size-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.favorite-btn:hover {
    color: var(--color-accent);
    transform: scale(1.1);
}

.favorite-btn.active i {
    color: var(--color-accent);
    font-weight: 900;
}

/* Estilos para la vista de imagen */
.chord-image-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    height: 300px;
    margin: 0 auto;
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border);
}

/* Estilos adicionales para la imagen */

.chord-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.image-loading, .image-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: var(--color-text);
}

.image-loading i, .image-error i {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
}

.image-error i {
    color: #e74c3c;
}

.image-source {
    text-align: center;
    margin-top: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

/* Estilos para diagrama de guitarra */
.guitar-neck {
    position: relative;
    width: 300px;
    height: 400px;
    background-color: #2C2C2C;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin: 0 auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
}

.guitar-strings {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.guitar-string {
    height: 2px;
    background-color: #B3B3B3;
    width: 100%;
    position: relative;
    box-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}

.guitar-string:nth-child(1),
.guitar-string:nth-child(2) {
    background-color: #D9D9D9;
    height: 1px;
}

.guitar-string:nth-child(3),
.guitar-string:nth-child(4) {
    height: 2px;
}

.guitar-string:nth-child(5),
.guitar-string:nth-child(6) {
    height: 3px;
}

.guitar-frets {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: var(--spacing-md);
    pointer-events: none;
}

.guitar-fret {
    height: 3px;
    background-color: #505050;
    width: 100%;
    margin: 30px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.guitar-dots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.guitar-dot {
    position: absolute;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--color-primary);
    transform: translate(-50%, -50%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
}

/* Estilos para diagrama de piano */
.piano-keyboard {
    width: 100%;
    max-width: 500px;
    height: 180px;
    position: relative;
    margin: 0 auto;
    perspective: 1000px;
}

.piano-keys {
    display: flex;
    height: 100%;
    position: relative;
    width: 100%;
    justify-content: center;
    transform-style: preserve-3d;
    transform: rotateX(5deg);
}

.piano-key {
    position: relative;
    border: 1px solid #000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.piano-key.white {
    background-color: #FFF;
    width: 45px;
    height: 100%;
    z-index: 1;
    position: relative;
    border-radius: 0 0 4px 4px;
    margin: 0 1px;
    background: linear-gradient(to bottom, #fff 0%, #f0f0f0 100%);
}

.piano-key.black {
    background-color: #000;
    width: 30px;
    height: 65%;
    position: absolute;
    z-index: 2;
    border-radius: 0 0 3px 3px;
    background: linear-gradient(to bottom, #333 0%, #000 100%);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.5);
}

/* Posicionamiento de las teclas */
.piano-key.white:nth-child(1) { left: 0; }
.piano-key.black:nth-child(2) { left: 30px; }
.piano-key.white:nth-child(3) { left: 47px; }
.piano-key.black:nth-child(4) { left: 77px; }
.piano-key.white:nth-child(5) { left: 94px; }
.piano-key.white:nth-child(6) { left: 141px; }
.piano-key.black:nth-child(7) { left: 171px; }
.piano-key.white:nth-child(8) { left: 188px; }
.piano-key.black:nth-child(9) { left: 218px; }
.piano-key.white:nth-child(10) { left: 235px; }
.piano-key.black:nth-child(11) { left: 265px; }
.piano-key.white:nth-child(12) { left: 282px; }

.piano-key.active {
    background: var(--color-primary);
    box-shadow: 0 0 10px var(--color-primary);
}

.piano-key.white.active {
    background: linear-gradient(to bottom, var(--color-primary) 0%, #1aa34a 100%);
}

.piano-key.black.active {
    background: linear-gradient(to bottom, #2ad65a 0%, var(--color-primary) 100%);
}

/* Estilos para diagrama de bajo */
.bass-neck {
    position: relative;
    width: 300px;
    height: 300px;
    background-color: #2C2C2C;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin: 0 auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid #444;
}

.bass-strings {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.bass-string {
    height: 3px;
    background-color: #B3B3B3;
    width: 100%;
    position: relative;
    box-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}

.bass-string:nth-child(1) {
    height: 2px;
}

.bass-string:nth-child(2) {
    height: 3px;
}

.bass-string:nth-child(3) {
    height: 4px;
}

.bass-string:nth-child(4) {
    height: 5px;
}

.bass-frets {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: var(--spacing-md);
    pointer-events: none;
}

.bass-fret {
    height: 3px;
    background-color: #505050;
    width: 100%;
    margin: 30px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.bass-dots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.bass-dot {
    position: absolute;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--color-primary);
    transform: translate(-50%, -50%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.chord-info {
    text-align: center;
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
}

.chord-variations {
    margin-top: var(--spacing-xl);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.chord-variations h3 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.variations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    justify-content: center;
}

.variation-card {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid var(--color-border);
}

.variation-card:hover {
    border-color: var(--color-primary);
    transform: translateY(-2px);
}

.variation-name {
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-sm);
}

.variation-preview {
    height: 80px;
    background-color: var(--color-background);
    border-radius: var(--border-radius-sm);
}

.related-chords {
    margin-top: var(--spacing-xxl);
    padding: var(--spacing-xl) 0;
    background-color: var(--color-background-light);
}

.related-chords h3 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.related-chords-grid {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.related-chord {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.related-chord:hover {
    background-color: var(--color-primary);
    color: var(--color-text);
}

/* Estilos para la sección de detalles del acorde */
.chord-details {
    margin-top: var(--spacing-xl);
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.chord-details h3 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-lg);
    color: var(--color-primary);
}

.chord-details-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.chord-theory, .chord-usage {
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
}

.chord-theory h4, .chord-usage h4 {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-md);
    color: var(--color-text);
    border-bottom: 1px solid var(--color-border);
    padding-bottom: var(--spacing-sm);
}

.chord-description {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
    color: var(--color-text-secondary);
}

.chord-notes {
    margin-top: var(--spacing-md);
}

.chord-notes p {
    margin-bottom: var(--spacing-sm);
}

.notes-list, .intervals-list {
    color: var(--color-primary);
    font-weight: var(--font-weight-medium);
}

.progressions p {
    margin-bottom: var(--spacing-sm);
}

.progressions-list {
    list-style: none;
    padding: 0;
}

.progressions-list li {
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: rgba(29, 185, 84, 0.1);
    border-radius: var(--border-radius-sm);
    display: inline-block;
    margin-right: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

/* Estilos para la carga de datos */
.loading {
    text-align: center;
    padding: var(--spacing-md);
    color: var(--color-text-secondary);
}

.loading i {
    margin-right: var(--spacing-sm);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Media Queries */
@media screen and (max-width: 768px) {
    .chord-controls {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }

    .chord-selector {
        width: 100%;
        max-width: 300px;
    }

    .guitar-neck,
    .bass-neck {
        width: 250px;
        height: 350px;
    }

    .piano-keyboard {
        max-width: 300px;
        height: 150px;
    }
}

@media screen and (max-width: 480px) {
    .instrument-selector {
        flex-direction: column;
        align-items: center;
    }

    .btn-instrument {
        width: 100%;
        max-width: 250px;
    }

    .guitar-neck,
    .bass-neck {
        width: 200px;
        height: 300px;
    }
}

/* Estilos para las posiciones de texto de acordes */
.chord-positions-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.chord-positions-text {
    position: relative;
}

.instrument-chord-positions {
    display: none;
}

.instrument-chord-positions.active {
    display: block;
}

.instrument-chord-positions h3 {
    color: #007bff;
    margin-bottom: 15px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chord-position-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.chord-position-info h4 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.chord-position-details p {
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.95rem;
}

.chord-position-details p:last-child {
    border-bottom: none;
}

.chord-position-details strong {
    color: #007bff;
    min-width: 120px;
    display: inline-block;
}

/* Estilos para variaciones de texto */
.variations-text {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.variation-text-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #007bff;
}

.variation-text-card h4 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1rem;
}

.variation-text-card p {
    margin: 5px 0;
    font-size: 0.9rem;
    color: #6c757d;
}

.variation-text-card strong {
    color: #007bff;
}
