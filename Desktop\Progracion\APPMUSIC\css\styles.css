/* Estilos generales - MusicApp */

/* Importar fuente similar a Spotify (Circular) */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

/* Variables CSS */
:root {
    /* Colores principales */
    --color-primary: #1DB954;       /* Verde Spotify */
    --color-primary-dark: #1AA34A;
    --color-secondary: #1E1E1E;     /* Negro/gris oscuro */
    --color-text: #FFFFFF;
    --color-text-secondary: #B3B3B3;
    --color-background: #121212;
    --color-background-light: #282828;
    --color-background-lighter: #333333;
    --color-border: #333333;

    /* Sombras */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);

    /* Espaciado */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Bordes */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --border-radius-circle: 50%;

    /* Transiciones */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Fuentes */
    --font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;
    --font-size-xxl: 2rem;
    --font-size-xxxl: 3rem;

    /* Pesos de fuente */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Tamaños de contenedor */
    --container-max-width: 1200px;
    --container-padding: 1rem;
}

/* Reset y estilos base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-regular);
    line-height: 1.5;
    color: var(--color-text);
    background-color: var(--color-background);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

a {
    color: var(--color-text);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

button, input, select, textarea {
    font-family: inherit;
    font-size: inherit;
    color: inherit;
}

button {
    cursor: pointer;
    border: none;
    background: none;
}

/* Contenedor */
.container {
    width: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

/* Navbar */
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background-color: var(--color-secondary);
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 100;
}

.logo h1 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
}

.logo a {
    color: var(--color-text);
    text-decoration: none;
}

.nav-links {
    display: flex;
    gap: 1.5rem;
}

.nav-links a {
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-fast);
    padding: 0.5rem 0;
    position: relative;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--color-text);
}

.nav-links a.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--color-primary);
}

.auth-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1.25rem;
    border-radius: var(--border-radius-md);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    text-align: center;
}

.btn-login {
    color: var(--color-text);
    background-color: transparent;
    border: 1px solid var(--color-border);
}

.btn-login:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: var(--color-text);
}

.btn-register {
    color: var(--color-text);
    background-color: var(--color-primary);
    border: 1px solid var(--color-primary);
}

.btn-register:hover {
    background-color: var(--color-primary-dark);
    border-color: var(--color-primary-dark);
    color: var(--color-text);
}

.burger {
    display: none;
    cursor: pointer;
}

.burger div {
    width: 25px;
    height: 3px;
    background-color: var(--color-text);
    margin: 5px;
    transition: all var(--transition-fast);
}

/* Botones */
.btn-primary {
    background-color: var(--color-primary);
    color: var(--color-text);
    border: 1px solid var(--color-primary);
}

.btn-primary:hover {
    background-color: var(--color-primary-dark);
    border-color: var(--color-primary-dark);
}

.btn-secondary {
    background-color: var(--color-background-light);
    color: var(--color-text);
    border: 1px solid var(--color-border);
}

.btn-secondary:hover {
    background-color: var(--color-background-lighter);
}

.btn-block {
    display: block;
    width: 100%;
}

.btn i {
    margin-right: 0.5rem;
}

.btn-cta {
    padding: 0.75rem 1.5rem;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-md);
    border-radius: var(--border-radius-md);
    background-color: var(--color-primary);
    color: var(--color-text);
    border: none;
    box-shadow: var(--shadow-md);
}

.btn-cta:hover {
    background-color: var(--color-primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--color-text);
}

/* Footer */
footer {
    background-color: var(--color-secondary);
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-xxl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    padding: 0 var(--spacing-xl);
}

.footer-logo h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
}

.footer-logo p {
    color: var(--color-text-secondary);
}

.footer-links h3,
.footer-contact h3 {
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
}

.footer-links ul {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.footer-links a {
    color: var(--color-text-secondary);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--color-primary);
}

.footer-contact p {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    color: var(--color-text-secondary);
}

.footer-contact i {
    margin-right: var(--spacing-sm);
    color: var(--color-primary);
}

.social-icons {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: var(--border-radius-circle);
    background-color: var(--color-background-light);
    color: var(--color-text);
    transition: all var(--transition-fast);
}

.social-icons a:hover {
    background-color: var(--color-primary);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-xl);
    margin-top: var(--spacing-xl);
    border-top: 1px solid var(--color-border);
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
}

/* Page Header */
.page-header {
    background-color: var(--color-background-light);
    padding: var(--spacing-xl) 0;
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.page-header h1 {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-sm);
}

.page-header p {
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Alertas y mensajes */
.alert {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 350px;
    background-color: var(--color-background-light);
    border-left: 4px solid var(--color-primary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-md);
    z-index: 1000;
    transform: translateX(120%);
    transition: transform var(--transition-normal);
}

.alert.show {
    transform: translateX(0);
}

.alert-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-content p {
    margin-right: var(--spacing-md);
}

.alert-close {
    background: none;
    border: none;
    color: var(--color-text-secondary);
    cursor: pointer;
    font-size: var(--font-size-lg);
}

.alert-info {
    border-color: #3498db;
}

.alert-success {
    border-color: var(--color-primary);
}

.alert-error {
    border-color: #e74c3c;
}

.alert-warning {
    border-color: #f39c12;
}

/* Media Queries */
@media screen and (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
}

@media screen and (max-width: 768px) {
    .navbar {
        padding: 1rem;
    }

    .nav-links {
        position: absolute;
        right: 0;
        top: 70px;
        background-color: var(--color-secondary);
        flex-direction: column;
        width: 50%;
        transform: translateX(100%);
        transition: transform var(--transition-normal);
        padding: 2rem;
        z-index: 100;
    }

    .nav-links.active {
        transform: translateX(0%);
    }

    .burger {
        display: block;
    }

    .auth-buttons {
        display: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .social-icons {
        justify-content: center;
    }
}

@media screen and (max-width: 480px) {
    html {
        font-size: 14px;
    }

    .nav-links {
        width: 100%;
    }
}
