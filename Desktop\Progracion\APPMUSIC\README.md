# 🎵 MusicApp - Aplicación de Teoría Musical

Una aplicación web moderna y completa para aprender teoría musical, acordes y escalas para guitarra, piano y bajo.

![MusicApp Banner](https://img.shields.io/badge/MusicApp-v2.0-blue?style=for-the-badge&logo=music)

## 🌟 Características Principales

### 🎸 Acordes
- **Múltiples instrumentos**: Guitarra, Piano y Bajo
- **Imágenes de alta calidad**: Cargadas desde JSONs separados por instrumento
- **Posiciones detalladas**: Información precisa de trastes, cuerdas y digitación
- **Sistema de favoritos**: Guarda tus acordes preferidos
- **Búsqueda avanzada**: Encuentra acordes por nombre, tipo o instrumento

### 🎼 Escalas
- **API de teoría musical**: Powered by Tonal.js para precisión máxima
- **Generación dinámica**: Escalas generadas en tiempo real
- **Múltiples modos**: <PERSON>, <PERSON><PERSON>, d<PERSON><PERSON><PERSON>, frigio, lidio, mixolidio y más
- **Posiciones del diapasón**: Visualización clara para cada instrumento
- **Información teórica**: Notas, intervalos y patrones de digitación

### ⚡ Rendimiento
- **Carga ultra-rápida**: Optimizado para velocidad máxima
- **Carga lazy**: Los datos se cargan solo cuando se necesitan
- **Sistema de caché**: Evita cargas repetitivas
- **Sin dependencias externas pesadas**: Funciona offline

## 🚀 Tecnologías Utilizadas

### Frontend
- **HTML5**: Estructura semántica moderna
- **CSS3**: Diseño responsive con Flexbox y Grid
- **JavaScript ES6+**: Funcionalidad moderna y eficiente
- **Font Awesome**: Iconografía profesional

### APIs y Librerías
- **Tonal.js**: Librería de teoría musical para escalas
- **Fetch API**: Comunicación asíncrona
- **LocalStorage**: Persistencia de favoritos

### Arquitectura
- **Modular**: Código organizado en módulos especializados
- **Responsive**: Adaptable a todos los dispositivos
- **Progressive**: Funciona sin conexión a internet

## 📁 Estructura del Proyecto

```
APPMUSIC/
├── 📄 index.html              # Página principal
├── 📄 README.md               # Este archivo
├── 📄 OPTIMIZACIONES_RENDIMIENTO.md
│
├── 📁 pages/                  # Páginas de la aplicación
│   ├── 📄 acordes.html        # Página de acordes
│   ├── 📄 escalas.html        # Página de escalas
│   ├── 📄 favoritos.html      # Página de favoritos
│   └── 📄 teoria.html         # Página de teoría
│
├── 📁 css/                    # Estilos
│   ├── 📄 main.css           # Estilos principales
│   ├── 📄 acordes.css        # Estilos específicos de acordes
│   ├── 📄 escalas.css        # Estilos específicos de escalas
│   ├── 📄 favoritos.css      # Estilos de favoritos
│   └── 📄 teoria.css         # Estilos de teoría
│
├── 📁 js/                     # JavaScript
│   ├── 📄 main.js            # Funcionalidad principal
│   ├── 📄 acordes.js         # Lógica de acordes
│   ├── 📄 escalas.js         # Lógica de escalas
│   ├── 📄 chord-data.js      # Servicio de datos de acordes
│   ├── 📄 scales-api.js      # API de escalas con Tonal.js
│   └── 📄 api-service.js     # Servicios generales
│
├── 📁 data/                   # Datos JSON
│   ├── 📄 acordes-guitarra.json  # Acordes de guitarra
│   ├── 📄 acordes-piano.json     # Acordes de piano
│   └── 📄 acordes-bajo.json      # Acordes de bajo
│
└── 📁 images/                 # Recursos gráficos
    └── 📄 logo.png           # Logo de la aplicación
```

## 🛠️ Instalación y Uso

### Requisitos Previos
- Navegador web moderno (Chrome, Firefox, Safari, Edge)
- Servidor web local (opcional, para desarrollo)

### Instalación Rápida

1. **Clonar o descargar** el proyecto:
```bash
git clone https://github.com/tu-usuario/musicapp.git
cd musicapp
```

2. **Abrir directamente** en el navegador:
```bash
# Opción 1: Abrir index.html directamente
open index.html

# Opción 2: Usar servidor local (recomendado)
python -m http.server 8000
# Luego ir a http://localhost:8000
```

3. **¡Listo!** La aplicación estará funcionando.

### Uso de la Aplicación

#### 🎸 Acordes
1. Selecciona el **instrumento** (Guitarra, Piano, Bajo)
2. Elige la **nota raíz** (C, D, E, F, G, A, B)
3. Selecciona el **tipo de acorde** (Mayor, Menor, 7, etc.)
4. Ve las **posiciones detalladas** y la **imagen del acorde**
5. **Marca como favorito** haciendo clic en el corazón

#### 🎼 Escalas
1. Selecciona el **instrumento**
2. Elige la **nota raíz**
3. Selecciona el **tipo de escala**
4. Explora las **posiciones generadas dinámicamente**
5. Ve las **notas de la escala** y **patrones de digitación**

## 🔧 Configuración Avanzada

### Personalizar Acordes
Para agregar nuevos acordes, edita los archivos JSON en `/data/`:

```json
{
  "C": {
    "major": {
      "name": "C Mayor",
      "imageUrl": "https://ejemplo.com/c-major.png",
      "positions": [
        {
          "name": "Posición abierta",
          "frets": {
            "6": "x",
            "5": "3",
            "4": "2",
            "3": "0",
            "2": "1",
            "1": "0"
          }
        }
      ]
    }
  }
}
```

### Configurar Escalas
Las escalas se generan automáticamente usando Tonal.js. Para agregar nuevos tipos:

```javascript
// En scales-api.js
this.availableScales = [
    'major', 'minor', 'dorian', 'tu-nueva-escala'
];
```

## 🎨 Personalización del Diseño

### Colores Principales
```css
:root {
    --primary-color: #007bff;    /* Azul principal */
    --secondary-color: #6c757d;  /* Gris secundario */
    --success-color: #28a745;    /* Verde éxito */
    --warning-color: #ffc107;    /* Amarillo advertencia */
    --danger-color: #dc3545;     /* Rojo peligro */
}
```

### Responsive Design
La aplicación es completamente responsive y se adapta a:
- 📱 **Móviles**: 320px - 768px
- 📱 **Tablets**: 768px - 1024px
- 💻 **Desktop**: 1024px+

## 🚀 Optimizaciones de Rendimiento

### Implementadas
- ✅ **Carga lazy** de datos por instrumento
- ✅ **Sistema de caché** inteligente
- ✅ **Compresión de imágenes** con SVG embebidos
- ✅ **Minificación** de código crítico
- ✅ **Eliminación de dependencias** innecesarias

### Métricas de Rendimiento
- ⚡ **Tiempo de carga inicial**: < 1 segundo
- 📦 **Tamaño total**: 70% más pequeño que v1.0
- 🌐 **Funciona offline**: 100%
- 📱 **Mobile-friendly**: Puntuación perfecta

## 🤝 Contribuir

### Cómo Contribuir
1. **Fork** el proyecto
2. Crea una **rama** para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. **Commit** tus cambios (`git commit -m 'Agregar nueva funcionalidad'`)
4. **Push** a la rama (`git push origin feature/nueva-funcionalidad`)
5. Abre un **Pull Request**

### Reportar Bugs
Usa el sistema de **Issues** de GitHub para reportar bugs:
- Describe el problema claramente
- Incluye pasos para reproducir
- Especifica navegador y versión
- Adjunta capturas de pantalla si es necesario

## 📝 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 👨‍💻 Autor

**Tu Nombre**
- GitHub: [@tu-usuario](https://github.com/tu-usuario)
- Email: <EMAIL>

## 🙏 Agradecimientos

- **Tonal.js** - Por la excelente librería de teoría musical
- **Font Awesome** - Por los iconos profesionales
- **Comunidad de desarrolladores** - Por el feedback y sugerencias

---

<div align="center">

**¿Te gusta MusicApp? ¡Dale una ⭐ en GitHub!**

[🐛 Reportar Bug](https://github.com/tu-usuario/musicapp/issues) • 
[✨ Solicitar Feature](https://github.com/tu-usuario/musicapp/issues) • 
[📖 Documentación](https://github.com/tu-usuario/musicapp/wiki)

</div>
