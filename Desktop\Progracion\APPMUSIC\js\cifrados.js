// JavaScript para la página de cifrados - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Elementos de la interfaz
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const cifradoText = document.getElementById('cifrado-text');
    const cifradoPreview = document.querySelector('.preview-content');
    const songTitle = document.getElementById('song-title');
    const songArtist = document.getElementById('song-artist');
    const songKey = document.getElementById('song-key');
    const songCapo = document.getElementById('song-capo');
    const previewButton = document.querySelector('.editor-actions .btn-secondary');
    const saveButton = document.querySelector('.editor-actions .btn-primary');
    const pdfUpload = document.getElementById('pdf-upload');
    const uploadArea = document.querySelector('.upload-area');
    
    // Verificar si el usuario está autenticado
    const isLoggedIn = MusicApp.getFromLocalStorage('musicapp_user')?.isLoggedIn || false;
    
    // Mostrar/ocultar elementos según el estado de autenticación
    const loginPrompt = document.querySelector('.login-prompt');
    if (loginPrompt) {
        loginPrompt.style.display = isLoggedIn ? 'none' : 'block';
    }
    
    // Cambiar entre pestañas
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // Desactivar todas las pestañas
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Activar la pestaña seleccionada
            this.classList.add('active');
            document.querySelector(`.${tabName}-tab`).classList.add('active');
        });
    });
    
    // Función para actualizar la vista previa del cifrado
    function updatePreview() {
        if (!cifradoText || !cifradoPreview) return;
        
        // Obtener valores
        const title = songTitle?.value || 'Título de la canción';
        const artist = songArtist?.value || 'Artista';
        const key = songKey?.value || 'C';
        const capo = songCapo?.value === '0' ? 'Sin capo' : `Capo ${songCapo.value}`;
        
        // Procesar el texto del cifrado
        let content = cifradoText.value;
        
        // Reemplazar secciones [Sección]
        content = content.replace(/\[(.*?)\]/g, '<p class="section-title">[$1]</p>');
        
        // Procesar líneas
        const lines = content.split('\n');
        let processedContent = '';
        let inChordLine = false;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            if (line === '') {
                processedContent += '<br>';
                continue;
            }
            
            // Detectar si es una línea de acordes (contiene principalmente acordes)
            const isChordLine = /^[A-G][#b]?(?:m|maj|dim|aug|sus|add)?[0-9]?(?:\s+[A-G][#b]?(?:m|maj|dim|aug|sus|add)?[0-9]?)*$/.test(line);
            
            if (isChordLine) {
                // Es una línea de acordes
                processedContent += `<p class="chord-line">${line}</p>`;
                inChordLine = true;
            } else if (inChordLine) {
                // Es una línea de letra que sigue a una línea de acordes
                processedContent += `<p class="text-line">${line}</p>`;
                inChordLine = false;
            } else {
                // Es una línea normal
                processedContent += `<p class="lyrics-line">${line}</p>`;
            }
        }
        
        // Actualizar la vista previa
        cifradoPreview.innerHTML = `
            <h2>${title}</h2>
            <h3>${artist}</h3>
            <p><strong>Tonalidad:</strong> ${key} | <strong>Capo:</strong> ${capo}</p>
            ${processedContent}
        `;
    }
    
    // Event listener para la vista previa
    if (previewButton) {
        previewButton.addEventListener('click', function() {
            updatePreview();
        });
    }
    
    // Event listeners para los campos del formulario
    if (songTitle) {
        songTitle.addEventListener('input', updatePreview);
    }
    
    if (songArtist) {
        songArtist.addEventListener('input', updatePreview);
    }
    
    if (songKey) {
        songKey.addEventListener('change', updatePreview);
    }
    
    if (songCapo) {
        songCapo.addEventListener('change', updatePreview);
    }
    
    // Event listener para el área de texto del cifrado
    if (cifradoText) {
        cifradoText.addEventListener('input', function() {
            // Actualizar la vista previa automáticamente si no es muy largo
            if (this.value.length < 1000) {
                updatePreview();
            }
        });
    }
    
    // Event listener para guardar el cifrado
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            if (!isLoggedIn) {
                MusicApp.showAlert('Debes iniciar sesión para guardar cifrados.', 'error');
                return;
            }
            
            // Validar que haya contenido
            if (!cifradoText.value.trim()) {
                MusicApp.showAlert('El cifrado no puede estar vacío.', 'error');
                return;
            }
            
            // Validar título
            if (!songTitle.value.trim()) {
                MusicApp.showAlert('Por favor, ingresa un título para el cifrado.', 'error');
                songTitle.focus();
                return;
            }
            
            // Simular guardado
            MusicApp.simulateLoading(this, function() {
                // Crear objeto de cifrado
                const cifrado = {
                    id: MusicApp.generateId(),
                    title: songTitle.value.trim(),
                    artist: songArtist.value.trim(),
                    key: songKey.value,
                    capo: songCapo.value,
                    content: cifradoText.value,
                    createdAt: new Date().toISOString(),
                    type: 'editor'
                };
                
                // Obtener cifrados existentes o crear array vacío
                const cifrados = MusicApp.getFromLocalStorage('musicapp_cifrados') || [];
                
                // Añadir nuevo cifrado
                cifrados.push(cifrado);
                
                // Guardar en localStorage
                MusicApp.saveToLocalStorage('musicapp_cifrados', cifrados);
                
                // Mostrar mensaje de éxito
                MusicApp.showAlert('Cifrado guardado correctamente.', 'success');
                
                // Actualizar la biblioteca (si estamos en la misma página)
                updateLibrary();
            });
        });
    }
    
    // Función para actualizar la biblioteca de cifrados
    function updateLibrary() {
        const libraryTab = document.querySelector('.library-tab');
        if (!libraryTab) return;
        
        // Obtener cifrados guardados
        const cifrados = MusicApp.getFromLocalStorage('musicapp_cifrados') || [];
        
        // Elementos de la biblioteca
        const libraryEmpty = libraryTab.querySelector('.library-empty');
        let libraryContent = libraryTab.querySelector('.library-content');
        
        // Si no hay cifrados, mostrar mensaje vacío
        if (cifrados.length === 0) {
            if (libraryEmpty) {
                libraryEmpty.style.display = 'block';
            }
            if (libraryContent) {
                libraryContent.remove();
            }
            return;
        }
        
        // Ocultar mensaje vacío
        if (libraryEmpty) {
            libraryEmpty.style.display = 'none';
        }
        
        // Crear contenedor de biblioteca si no existe
        if (!libraryContent) {
            libraryContent = document.createElement('div');
            libraryContent.className = 'library-content';
            libraryTab.appendChild(libraryContent);
        }
        
        // Limpiar contenido anterior
        libraryContent.innerHTML = '';
        
        // Crear grid de cifrados
        const cifradosGrid = document.createElement('div');
        cifradosGrid.className = 'cifrados-grid';
        
        // Añadir cada cifrado
        cifrados.forEach(cifrado => {
            const card = document.createElement('div');
            card.className = 'cifrado-card';
            card.innerHTML = `
                <div class="cifrado-card-header">
                    <h3>${cifrado.title}</h3>
                    <p>${cifrado.artist || 'Sin artista'}</p>
                </div>
                <div class="cifrado-card-body">
                    <p><strong>Tonalidad:</strong> ${cifrado.key}</p>
                    <p><strong>Capo:</strong> ${cifrado.capo === '0' ? 'Sin capo' : `Capo ${cifrado.capo}`}</p>
                    <p><strong>Creado:</strong> ${MusicApp.formatDate(cifrado.createdAt)}</p>
                </div>
                <div class="cifrado-card-footer">
                    <button class="btn btn-sm btn-secondary view-btn" data-id="${cifrado.id}">
                        <i class="fas fa-eye"></i> Ver
                    </button>
                    <button class="btn btn-sm btn-secondary edit-btn" data-id="${cifrado.id}">
                        <i class="fas fa-edit"></i> Editar
                    </button>
                    <button class="btn btn-sm btn-danger delete-btn" data-id="${cifrado.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            
            cifradosGrid.appendChild(card);
        });
        
        // Añadir grid al contenedor
        libraryContent.appendChild(cifradosGrid);
        
        // Añadir event listeners a los botones
        const viewButtons = libraryContent.querySelectorAll('.view-btn');
        const editButtons = libraryContent.querySelectorAll('.edit-btn');
        const deleteButtons = libraryContent.querySelectorAll('.delete-btn');
        
        // Ver cifrado
        viewButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const cifrado = cifrados.find(c => c.id === id);
                
                if (cifrado) {
                    // Aquí se podría implementar una vista detallada del cifrado
                    MusicApp.showAlert(`Visualizando cifrado: ${cifrado.title}`, 'info');
                }
            });
        });
        
        // Editar cifrado
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const cifrado = cifrados.find(c => c.id === id);
                
                if (cifrado) {
                    // Cambiar a la pestaña de editor
                    document.querySelector('.tab-btn[data-tab="editor"]').click();
                    
                    // Cargar datos en el editor
                    if (songTitle) songTitle.value = cifrado.title;
                    if (songArtist) songArtist.value = cifrado.artist || '';
                    if (songKey) songKey.value = cifrado.key;
                    if (songCapo) songCapo.value = cifrado.capo;
                    if (cifradoText) cifradoText.value = cifrado.content;
                    
                    // Actualizar vista previa
                    updatePreview();
                    
                    MusicApp.showAlert(`Editando cifrado: ${cifrado.title}`, 'info');
                }
            });
        });
        
        // Eliminar cifrado
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                
                // Confirmar eliminación
                if (confirm('¿Estás seguro de que deseas eliminar este cifrado? Esta acción no se puede deshacer.')) {
                    // Filtrar el cifrado a eliminar
                    const updatedCifrados = cifrados.filter(c => c.id !== id);
                    
                    // Guardar en localStorage
                    MusicApp.saveToLocalStorage('musicapp_cifrados', updatedCifrados);
                    
                    // Actualizar la biblioteca
                    updateLibrary();
                    
                    MusicApp.showAlert('Cifrado eliminado correctamente.', 'success');
                }
            });
        });
    }
    
    // Drag and drop para subir PDF
    if (uploadArea && pdfUpload) {
        // Prevenir comportamiento por defecto
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        // Resaltar área al arrastrar
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            uploadArea.classList.add('highlight');
        }
        
        function unhighlight() {
            uploadArea.classList.remove('highlight');
        }
        
        // Manejar archivos soltados
        uploadArea.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            if (files.length > 0) {
                pdfUpload.files = files;
                handleFiles(files);
            }
        }
        
        // Manejar selección de archivos
        pdfUpload.addEventListener('change', function() {
            handleFiles(this.files);
        });
        
        function handleFiles(files) {
            if (files.length === 0) return;
            
            const file = files[0];
            
            // Verificar tipo de archivo
            if (file.type !== 'application/pdf') {
                MusicApp.showAlert('Por favor, selecciona un archivo PDF.', 'error');
                return;
            }
            
            // Verificar tamaño (máximo 10MB)
            if (file.size > 10 * 1024 * 1024) {
                MusicApp.showAlert('El archivo es demasiado grande. El tamaño máximo es 10MB.', 'error');
                return;
            }
            
            // Mostrar nombre del archivo
            const fileInfo = document.createElement('p');
            fileInfo.className = 'selected-file';
            fileInfo.innerHTML = `<i class="fas fa-file-pdf"></i> ${file.name}`;
            
            // Eliminar info anterior si existe
            const oldInfo = uploadArea.querySelector('.selected-file');
            if (oldInfo) {
                oldInfo.remove();
            }
            
            uploadArea.appendChild(fileInfo);
            
            MusicApp.showAlert('Archivo seleccionado correctamente.', 'success');
        }
    }
    
    // Inicializar la biblioteca si el usuario está autenticado
    if (isLoggedIn) {
        updateLibrary();
    }
});
