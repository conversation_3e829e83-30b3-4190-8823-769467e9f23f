// API Service simplificado para MusicApp
// Este archivo maneja solo los datos locales para mejor rendimiento

// Configuración simplificada
const API_CONFIG = {
    // Solo usar datos locales para mejor rendimiento
    useLocalOnly: true,
    // Sistema de caché simple
    cache: {
        enabled: true,
        duration: 3600000, // 1 hora
        storage: new Map()
    }
};

// Sistema de caché simple
const CacheManager = {
    // Generar clave de caché
    generateKey: function(endpoint, params = {}) {
        const paramString = Object.keys(params)
            .sort()
            .map(key => `${key}=${params[key]}`)
            .join('&');
        return `${endpoint}?${paramString}`;
    },

    // Obtener datos del caché
    get: function(key) {
        if (!API_CONFIG.cache.enabled) return null;
        const cached = API_CONFIG.cache.storage.get(key);
        if (!cached) return null;

        // Verificar si el caché ha expirado
        if (Date.now() - cached.timestamp > API_CONFIG.cache.duration) {
            API_CONFIG.cache.storage.delete(key);
            return null;
        }
        return cached.data;
    },

    // Guardar datos en el caché
    set: function(key, data) {
        if (!API_CONFIG.cache.enabled) return;
        API_CONFIG.cache.storage.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }
};

// Función simple para datos locales solamente
function getLocalData(type, params = {}) {
    // Solo trabajamos con datos locales para mejor rendimiento
    const cacheKey = CacheManager.generateKey(type, params);
    const cached = CacheManager.get(cacheKey);

    if (cached) {
        return cached;
    }

    // Los datos se manejan directamente desde chord-data.js
    return {
        success: false,
        error: 'Usar ChordDataService para datos locales'
    };
}

// Simulador simple de acordes - solo datos básicos
const chordApiSimulator = {
    // Base de datos simplificada
    basicChords: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
    chordTypes: ['major', 'minor', '7', 'maj7', 'min7'],

    // Método simple para verificar si existe un acorde
    hasChord: function(instrument, root, type = 'major') {
        return this.basicChords.includes(root) && this.chordTypes.includes(type);
    },

    // Método simple para buscar acordes
    searchChords: function(query) {
        const results = [];
        const queryLower = query.toLowerCase();

        // Buscar en acordes básicos
        for (const root of this.basicChords) {
            for (const type of this.chordTypes) {
                const chordName = `${root} ${type}`;
                if (chordName.toLowerCase().includes(queryLower)) {
                    results.push({
                        instrument: 'guitar',
                        root,
                        type,
                        name: chordName
                    });
                }
            }
        }

        return {
            success: true,
            count: results.length,
            data: results
        };
    }
};

// Servicio simplificado para acordes - solo datos locales
const chordApiService = {
    // Obtener imagen de un acorde - simplificado
    getChordImage: function(instrument, root, type = 'major') {
        // Usar solo el servicio de datos local
        if (window.ChordDataService) {
            return window.ChordDataService.getChordImage(instrument, root, type);
        }

        return {
            success: false,
            error: 'ChordDataService no disponible'
        };
    },

    // Buscar acordes - simplificado
    searchChords: function(query, instrument = 'all') {
        // Usar solo el simulador simple
        return chordApiSimulator.searchChords(query);
    },

    // Obtener información de un acorde - simplificado
    getChordInfo: function(instrument, root, type = 'major') {
        // Usar solo datos locales
        if (window.ChordDataService) {
            // Intentar obtener desde ChordDataService primero
            const result = window.ChordDataService.getChordImage(instrument, root, type);
            if (result.success) {
                return result;
            }
        }

        // Fallback al simulador simple
        return {
            success: true,
            data: {
                name: `${root} ${type}`,
                notes: this.getBasicNotes(root, type),
                description: `Acorde ${root} ${type}`
            }
        };
    },

    // Obtener notas básicas de un acorde
    getBasicNotes: function(root, type) {
        const notes = {
            'C': ['C', 'E', 'G'],
            'D': ['D', 'F#', 'A'],
            'E': ['E', 'G#', 'B'],
            'F': ['F', 'A', 'C'],
            'G': ['G', 'B', 'D'],
            'A': ['A', 'C#', 'E'],
            'B': ['B', 'D#', 'F#']
        };

        return notes[root] || ['C', 'E', 'G'];
    }
};

// Inicialización simplificada
function initializeApiService() {
    console.log('API Service simplificado inicializado');
}

// Exportar solo lo necesario
window.MusicAppApi = {
    chordApiSimulator,
    chordApiService,
    CacheManager
};

// Inicializar automáticamente
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApiService);
} else {
    initializeApiService();
}

