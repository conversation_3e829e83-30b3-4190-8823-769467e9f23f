/* Estilos para autenticación - MusicApp */

.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: var(--spacing-xl) var(--spacing-md);
}

.auth-card {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    width: 100%;
    max-width: 500px;
    overflow: hidden;
}

.auth-header {
    padding: var(--spacing-xl);
    text-align: center;
    border-bottom: 1px solid var(--color-border);
}

.auth-header h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
}

.auth-header p {
    color: var(--color-text-secondary);
}

.auth-form {
    padding: var(--spacing-xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-text-secondary);
}

.input-with-icon .toggle-password {
    left: auto;
    right: var(--spacing-md);
    cursor: pointer;
}

.input-with-icon input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) calc(var(--spacing-md) * 3);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    background-color: var(--color-background);
    color: var(--color-text);
    transition: border-color var(--transition-fast);
}

.input-with-icon input:focus {
    outline: none;
    border-color: var(--color-primary);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-sm);
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input[type="checkbox"] {
    margin-right: var(--spacing-sm);
    accent-color: var(--color-primary);
}

.forgot-password {
    color: var(--color-primary);
}

.forgot-password:hover {
    text-decoration: underline;
}

.social-login {
    margin-top: var(--spacing-xl);
    text-align: center;
}

.social-login p {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.social-login p::before,
.social-login p::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 30%;
    height: 1px;
    background-color: var(--color-border);
}

.social-login p::before {
    left: 0;
}

.social-login p::after {
    right: 0;
}

.social-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.btn-social {
    flex: 1;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.btn-social i {
    margin-right: var(--spacing-sm);
}

.btn-google {
    background-color: #DB4437;
    color: white;
}

.btn-google:hover {
    background-color: #C53929;
}

.btn-facebook {
    background-color: #4267B2;
    color: white;
}

.btn-facebook:hover {
    background-color: #365899;
}

.auth-footer {
    padding: var(--spacing-lg);
    text-align: center;
    border-top: 1px solid var(--color-border);
    background-color: rgba(0, 0, 0, 0.1);
}

.auth-footer a {
    color: var(--color-primary);
    font-weight: var(--font-weight-medium);
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Estilos específicos para registro */
.user-type-selector {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
}

.user-type {
    flex: 1;
}

.user-type input[type="radio"] {
    display: none;
}

.user-type label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    background-color: var(--color-background);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.user-type input[type="radio"]:checked + label {
    border-color: var(--color-primary);
    background-color: rgba(29, 185, 84, 0.1);
}

.user-type label i {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
    color: var(--color-text-secondary);
}

.user-type input[type="radio"]:checked + label i {
    color: var(--color-primary);
}

.user-type label span {
    font-weight: var(--font-weight-medium);
}

.terms {
    display: flex;
    align-items: flex-start;
}

.terms input[type="checkbox"] {
    margin-right: var(--spacing-sm);
    margin-top: 5px;
    accent-color: var(--color-primary);
}

.terms label {
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.terms a {
    color: var(--color-primary);
}

.terms a:hover {
    text-decoration: underline;
}

/* Media Queries */
@media screen and (max-width: 768px) {
    .auth-card {
        max-width: 100%;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .social-buttons {
        flex-direction: column;
    }
}

@media screen and (max-width: 480px) {
    .auth-form {
        padding: var(--spacing-lg);
    }
    
    .user-type-selector {
        flex-direction: column;
    }
}
