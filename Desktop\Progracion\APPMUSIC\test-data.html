<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Carga de Datos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .test-section {
            background: #e9ecef;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        pre {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 Test de Carga de Datos - MusicApp</h1>
    
    <div class="test-section">
        <h2>📁 Test de JSONs de Acordes</h2>
        <div id="chord-test-results"></div>
    </div>

    <div class="test-section">
        <h2>🎼 Test de API de Escalas</h2>
        <div id="scale-test-results"></div>
    </div>

    <div class="test-section">
        <h2>🖼️ Test de Imágenes Generadas</h2>
        <div id="image-test-results"></div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/tonal@5.0.0/browser/tonal.min.js"></script>
    <script src="js/chord-data.js"></script>
    <script src="js/scales-api.js"></script>

    <script>
        async function testChordData() {
            const resultsDiv = document.getElementById('chord-test-results');
            resultsDiv.innerHTML = '<p class="info">🔄 Probando carga de acordes...</p>';

            try {
                // Inicializar servicio
                await ChordDataService.init();
                
                // Probar acordes de guitarra
                const guitarChord = ChordDataService.getChordImage('guitar', 'C', 'major');
                console.log('Acorde de guitarra C mayor:', guitarChord);
                
                // Probar acordes de piano
                const pianoChord = ChordDataService.getChordImage('piano', 'C', 'major');
                console.log('Acorde de piano C mayor:', pianoChord);
                
                // Probar acordes de bajo
                const bassChord = ChordDataService.getChordImage('bass', 'C', 'major');
                console.log('Acorde de bajo C mayor:', bassChord);

                // Probar búsqueda
                const searchResults = await ChordDataService.searchChords('C');
                console.log('Resultados de búsqueda:', searchResults);

                let html = '<p class="success">✅ Acordes cargados correctamente!</p>';
                html += `<p><strong>Guitarra C mayor:</strong> ${guitarChord.success ? '✅' : '❌'} (${guitarChord.data.source})</p>`;
                html += `<p><strong>Piano C mayor:</strong> ${pianoChord.success ? '✅' : '❌'} (${pianoChord.data.source})</p>`;
                html += `<p><strong>Bajo C mayor:</strong> ${bassChord.success ? '✅' : '❌'} (${bassChord.data.source})</p>`;
                html += `<p><strong>Búsqueda "C":</strong> ${searchResults.count} resultados encontrados</p>`;
                
                if (guitarChord.success && guitarChord.data.imageUrl) {
                    html += `<p><strong>Imagen de guitarra:</strong></p>`;
                    html += `<img src="${guitarChord.data.imageUrl}" alt="C mayor guitarra" style="max-width: 200px; border: 1px solid #ccc;">`;
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                console.error('Error en test de acordes:', error);
                resultsDiv.innerHTML = `<p class="error">❌ Error: ${error.message}</p>`;
            }
        }

        async function testScaleAPI() {
            const resultsDiv = document.getElementById('scale-test-results');
            resultsDiv.innerHTML = '<p class="info">🔄 Probando API de escalas...</p>';

            try {
                // Verificar que Tonal.js esté disponible
                if (typeof Tonal === 'undefined') {
                    throw new Error('Tonal.js no está disponible');
                }

                // Probar escalas para diferentes instrumentos
                const guitarScale = await window.ScalesApiService.getScaleForInstrument('C', 'major', 'guitar');
                const pianoScale = await window.ScalesApiService.getScaleForInstrument('C', 'major', 'piano');
                const bassScale = await window.ScalesApiService.getScaleForInstrument('C', 'major', 'bass');

                console.log('Escala de guitarra:', guitarScale);
                console.log('Escala de piano:', pianoScale);
                console.log('Escala de bajo:', bassScale);

                let html = '<p class="success">✅ API de escalas funcionando!</p>';
                html += `<p><strong>Tonal.js:</strong> ${typeof Tonal !== 'undefined' ? '✅ Disponible' : '❌ No disponible'}</p>`;
                html += `<p><strong>Guitarra C mayor:</strong> ${guitarScale.success ? '✅' : '❌'} (${guitarScale.source || 'N/A'})</p>`;
                html += `<p><strong>Piano C mayor:</strong> ${pianoScale.success ? '✅' : '❌'} (${pianoScale.source || 'N/A'})</p>`;
                html += `<p><strong>Bajo C mayor:</strong> ${bassScale.success ? '✅' : '❌'} (${bassScale.source || 'N/A'})</p>`;

                if (guitarScale.success) {
                    html += `<p><strong>Notas de la escala:</strong> ${guitarScale.data.scale.notes.join(' - ')}</p>`;
                    html += `<p><strong>Posiciones de guitarra:</strong> ${guitarScale.data.positions.length} encontradas</p>`;
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                console.error('Error en test de escalas:', error);
                resultsDiv.innerHTML = `<p class="error">❌ Error: ${error.message}</p>`;
            }
        }

        async function testGeneratedImages() {
            const resultsDiv = document.getElementById('image-test-results');
            resultsDiv.innerHTML = '<p class="info">🔄 Probando generación de imágenes...</p>';

            try {
                // Generar imagen de escala de guitarra
                const guitarScale = await window.ScalesApiService.getScaleForInstrument('C', 'major', 'guitar');
                
                let html = '<p class="success">✅ Imágenes generadas correctamente!</p>';
                
                if (guitarScale.success && guitarScale.data.imageUrl) {
                    html += '<p><strong>Escala C mayor - Guitarra:</strong></p>';
                    html += `<img src="${guitarScale.data.imageUrl}" alt="Escala C mayor guitarra" style="max-width: 400px; border: 1px solid #ccc;">`;
                }

                // Generar imagen de escala de piano
                const pianoScale = await window.ScalesApiService.getScaleForInstrument('C', 'major', 'piano');
                
                if (pianoScale.success && pianoScale.data.imageUrl) {
                    html += '<p><strong>Escala C mayor - Piano:</strong></p>';
                    html += `<img src="${pianoScale.data.imageUrl}" alt="Escala C mayor piano" style="max-width: 400px; border: 1px solid #ccc;">`;
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                console.error('Error en test de imágenes:', error);
                resultsDiv.innerHTML = `<p class="error">❌ Error: ${error.message}</p>`;
            }
        }

        // Ejecutar tests al cargar la página
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🧪 Iniciando tests...');
            
            await testChordData();
            await testScaleAPI();
            await testGeneratedImages();
            
            console.log('🧪 Tests completados');
        });
    </script>
</body>
</html>
