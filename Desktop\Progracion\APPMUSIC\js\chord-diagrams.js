// Generador de diagramas de acordes en SVG
// Este archivo genera diagramas visuales para acordes de guitarra, piano y bajo

const ChordDiagramGenerator = {
    // Datos básicos de acordes para guitarra
    guitarChords: {
        'C': {
            major: { frets: [0, 1, 0, 2, 3, 0], fingers: [0, 1, 0, 2, 3, 0], muted: [] },
            minor: { frets: [0, 1, 3, 3, 2, 1], fingers: [0, 1, 3, 4, 2, 1], muted: [] },
            '7': { frets: [0, 1, 0, 2, 1, 0], fingers: [0, 1, 0, 3, 2, 0], muted: [] }
        },
        'D': {
            major: { frets: [-1, -1, 0, 2, 3, 2], fingers: [0, 0, 0, 1, 3, 2], muted: [0, 1] },
            minor: { frets: [-1, -1, 0, 2, 3, 1], fingers: [0, 0, 0, 2, 3, 1], muted: [0, 1] },
            '7': { frets: [-1, -1, 0, 2, 1, 2], fingers: [0, 0, 0, 2, 1, 3], muted: [0, 1] }
        },
        'E': {
            major: { frets: [0, 2, 2, 1, 0, 0], fingers: [0, 2, 3, 1, 0, 0], muted: [] },
            minor: { frets: [0, 2, 2, 0, 0, 0], fingers: [0, 2, 3, 0, 0, 0], muted: [] },
            '7': { frets: [0, 2, 0, 1, 0, 0], fingers: [0, 2, 0, 1, 0, 0], muted: [] }
        },
        'F': {
            major: { frets: [1, 3, 3, 2, 1, 1], fingers: [1, 3, 4, 2, 1, 1], muted: [] },
            minor: { frets: [1, 3, 3, 1, 1, 1], fingers: [1, 3, 4, 1, 1, 1], muted: [] },
            '7': { frets: [1, 3, 1, 2, 1, 1], fingers: [1, 3, 1, 2, 1, 1], muted: [] }
        },
        'G': {
            major: { frets: [3, 2, 0, 0, 3, 3], fingers: [3, 2, 0, 0, 4, 4], muted: [] },
            minor: { frets: [3, 5, 5, 3, 3, 3], fingers: [1, 3, 4, 1, 1, 1], muted: [] },
            '7': { frets: [3, 2, 0, 0, 0, 1], fingers: [3, 2, 0, 0, 0, 1], muted: [] }
        },
        'A': {
            major: { frets: [-1, 0, 2, 2, 2, 0], fingers: [0, 0, 1, 2, 3, 0], muted: [0] },
            minor: { frets: [-1, 0, 2, 2, 1, 0], fingers: [0, 0, 2, 3, 1, 0], muted: [0] },
            '7': { frets: [-1, 0, 2, 0, 2, 0], fingers: [0, 0, 2, 0, 3, 0], muted: [0] }
        },
        'B': {
            major: { frets: [-1, 2, 4, 4, 4, 2], fingers: [0, 1, 2, 3, 4, 1], muted: [0] },
            minor: { frets: [-1, 2, 4, 4, 3, 2], fingers: [0, 1, 3, 4, 2, 1], muted: [0] },
            '7': { frets: [-1, 2, 1, 2, 0, 2], fingers: [0, 2, 1, 3, 0, 4], muted: [0] }
        }
    },

    // Generar diagrama SVG para guitarra
    generateGuitarDiagram: function(root, type = 'major') {
        const chordData = this.guitarChords[root] && this.guitarChords[root][type];
        if (!chordData) {
            return this.generatePlaceholderDiagram(root, type, 'guitarra');
        }

        const { frets, fingers, muted } = chordData;
        const width = 200;
        const height = 250;
        const stringSpacing = 30;
        const fretSpacing = 35;
        const startX = 30;
        const startY = 50;

        let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
        
        // Fondo
        svg += `<rect width="${width}" height="${height}" fill="white" stroke="#ddd" stroke-width="1"/>`;
        
        // Título
        svg += `<text x="${width/2}" y="25" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">${root} ${type}</text>`;
        
        // Dibujar cuerdas (verticales)
        for (let i = 0; i < 6; i++) {
            const x = startX + i * stringSpacing;
            svg += `<line x1="${x}" y1="${startY}" x2="${x}" y2="${startY + 4 * fretSpacing}" stroke="#333" stroke-width="2"/>`;
        }
        
        // Dibujar trastes (horizontales)
        for (let i = 0; i <= 4; i++) {
            const y = startY + i * fretSpacing;
            const strokeWidth = i === 0 ? 4 : 1; // Cejuela más gruesa
            svg += `<line x1="${startX}" y1="${y}" x2="${startX + 5 * stringSpacing}" y2="${y}" stroke="#333" stroke-width="${strokeWidth}"/>`;
        }
        
        // Dibujar posiciones de dedos
        for (let i = 0; i < 6; i++) {
            const x = startX + i * stringSpacing;
            const fret = frets[i];
            
            if (muted.includes(i)) {
                // Cuerda silenciada
                svg += `<text x="${x}" y="${startY - 10}" text-anchor="middle" font-family="Arial" font-size="14" fill="red">×</text>`;
            } else if (fret === 0) {
                // Cuerda al aire
                svg += `<circle cx="${x}" cy="${startY - 15}" r="6" fill="none" stroke="#333" stroke-width="2"/>`;
            } else if (fret > 0) {
                // Dedo en traste
                const y = startY + (fret - 0.5) * fretSpacing;
                svg += `<circle cx="${x}" cy="${y}" r="8" fill="#333"/>`;
                
                // Número de dedo
                const finger = fingers[i];
                if (finger > 0) {
                    svg += `<text x="${x}" y="${y + 4}" text-anchor="middle" font-family="Arial" font-size="10" fill="white">${finger}</text>`;
                }
            }
        }
        
        svg += '</svg>';
        return svg;
    },

    // Generar diagrama SVG para piano
    generatePianoDiagram: function(root, type = 'major') {
        const width = 300;
        const height = 120;
        const whiteKeyWidth = 30;
        const blackKeyWidth = 20;
        const blackKeyHeight = 70;
        
        // Notas de acordes básicos
        const chordNotes = {
            'C': { major: ['C', 'E', 'G'], minor: ['C', 'Eb', 'G'], '7': ['C', 'E', 'G', 'Bb'] },
            'D': { major: ['D', 'F#', 'A'], minor: ['D', 'F', 'A'], '7': ['D', 'F#', 'A', 'C'] },
            'E': { major: ['E', 'G#', 'B'], minor: ['E', 'G', 'B'], '7': ['E', 'G#', 'B', 'D'] },
            'F': { major: ['F', 'A', 'C'], minor: ['F', 'Ab', 'C'], '7': ['F', 'A', 'C', 'Eb'] },
            'G': { major: ['G', 'B', 'D'], minor: ['G', 'Bb', 'D'], '7': ['G', 'B', 'D', 'F'] },
            'A': { major: ['A', 'C#', 'E'], minor: ['A', 'C', 'E'], '7': ['A', 'C#', 'E', 'G'] },
            'B': { major: ['B', 'D#', 'F#'], minor: ['B', 'D', 'F#'], '7': ['B', 'D#', 'F#', 'A'] }
        };

        const notes = chordNotes[root] && chordNotes[root][type] || [];
        
        let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
        
        // Fondo
        svg += `<rect width="${width}" height="${height}" fill="white" stroke="#ddd" stroke-width="1"/>`;
        
        // Título
        svg += `<text x="${width/2}" y="15" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">${root} ${type}</text>`;
        
        // Teclas blancas
        const whiteKeys = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];
        for (let i = 0; i < whiteKeys.length; i++) {
            const x = 20 + i * whiteKeyWidth;
            const isActive = notes.includes(whiteKeys[i]);
            const fill = isActive ? '#4CAF50' : 'white';
            
            svg += `<rect x="${x}" y="25" width="${whiteKeyWidth-1}" height="70" fill="${fill}" stroke="#333" stroke-width="1"/>`;
            svg += `<text x="${x + whiteKeyWidth/2}" y="110" text-anchor="middle" font-family="Arial" font-size="10">${whiteKeys[i]}</text>`;
        }
        
        // Teclas negras
        const blackKeys = [
            { note: 'C#', x: 20 + 0.7 * whiteKeyWidth },
            { note: 'D#', x: 20 + 1.7 * whiteKeyWidth },
            { note: 'F#', x: 20 + 3.7 * whiteKeyWidth },
            { note: 'G#', x: 20 + 4.7 * whiteKeyWidth },
            { note: 'A#', x: 20 + 5.7 * whiteKeyWidth }
        ];
        
        for (const key of blackKeys) {
            const isActive = notes.includes(key.note) || notes.includes(key.note.replace('#', 'b'));
            const fill = isActive ? '#2E7D32' : '#333';
            
            svg += `<rect x="${key.x}" y="25" width="${blackKeyWidth}" height="${blackKeyHeight}" fill="${fill}" stroke="#000" stroke-width="1"/>`;
        }
        
        svg += '</svg>';
        return svg;
    },

    // Generar diagrama placeholder
    generatePlaceholderDiagram: function(root, type, instrument) {
        const width = 250;
        const height = 150;
        
        let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
        svg += `<rect width="${width}" height="${height}" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>`;
        svg += `<text x="${width/2}" y="${height/2 - 10}" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#495057">${root} ${type}</text>`;
        svg += `<text x="${width/2}" y="${height/2 + 15}" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">${instrument}</text>`;
        svg += '</svg>';
        
        return svg;
    },

    // Función principal para generar diagrama
    generateDiagram: function(instrument, root, type = 'major') {
        let svgContent = '';
        
        switch (instrument) {
            case 'guitar':
                svgContent = this.generateGuitarDiagram(root, type);
                break;
            case 'piano':
                svgContent = this.generatePianoDiagram(root, type);
                break;
            case 'bass':
                svgContent = this.generatePlaceholderDiagram(root, type, 'bajo');
                break;
            default:
                svgContent = this.generatePlaceholderDiagram(root, type, instrument);
        }
        
        // Convertir SVG a data URL
        return `data:image/svg+xml;base64,${btoa(svgContent)}`;
    }
};

// Exportar para uso global
window.ChordDiagramGenerator = ChordDiagramGenerator;
