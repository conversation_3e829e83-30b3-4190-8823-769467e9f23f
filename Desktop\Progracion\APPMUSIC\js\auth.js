// JavaScript para autenticación - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Formulario de login
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validar formulario
            if (!MusicApp.validateForm(this)) {
                MusicApp.showAlert('Por favor, completa todos los campos requeridos.', 'error');
                return;
            }
            
            // Obtener datos del formulario
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember')?.checked || false;
            
            // Simular envío del formulario
            const submitButton = this.querySelector('button[type="submit"]');
            MusicApp.simulateLoading(submitButton, function() {
                // Simular autenticación exitosa
                // En una aplicación real, aquí se haría la petición al servidor
                
                // Guardar datos de sesión en localStorage
                const userData = {
                    id: MusicApp.generateId(),
                    email: email,
                    name: 'Usuario Demo',
                    type: 'musician',
                    isLoggedIn: true,
                    loginDate: new Date().toISOString()
                };
                
                MusicApp.saveToLocalStorage('musicapp_user', userData);
                
                // Mostrar mensaje de éxito
                MusicApp.showAlert('Inicio de sesión exitoso. Redirigiendo...', 'success');
                
                // Redireccionar al dashboard o página principal
                setTimeout(function() {
                    window.location.href = '../index.html';
                }, 1500);
            }, 1500);
        });
    }
    
    // Formulario de registro
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validar formulario
            if (!MusicApp.validateForm(this)) {
                MusicApp.showAlert('Por favor, completa todos los campos requeridos.', 'error');
                return;
            }
            
            // Validar que las contraseñas coincidan
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            if (password !== confirmPassword) {
                MusicApp.showAlert('Las contraseñas no coinciden.', 'error');
                return;
            }
            
            // Obtener datos del formulario
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const userType = document.querySelector('input[name="user-type"]:checked').value;
            const terms = document.getElementById('terms').checked;
            
            if (!terms) {
                MusicApp.showAlert('Debes aceptar los términos y condiciones.', 'error');
                return;
            }
            
            // Simular envío del formulario
            const submitButton = this.querySelector('button[type="submit"]');
            MusicApp.simulateLoading(submitButton, function() {
                // Simular registro exitoso
                // En una aplicación real, aquí se haría la petición al servidor
                
                // Guardar datos de sesión en localStorage
                const userData = {
                    id: MusicApp.generateId(),
                    email: email,
                    name: name,
                    type: userType,
                    isLoggedIn: true,
                    registrationDate: new Date().toISOString()
                };
                
                MusicApp.saveToLocalStorage('musicapp_user', userData);
                
                // Mostrar mensaje de éxito
                MusicApp.showAlert('Registro exitoso. Redirigiendo...', 'success');
                
                // Redireccionar al dashboard o página principal
                setTimeout(function() {
                    window.location.href = '../index.html';
                }, 1500);
            }, 2000);
        });
    }
    
    // Función para verificar si el usuario está autenticado
    function checkAuth() {
        const userData = MusicApp.getFromLocalStorage('musicapp_user');
        return userData && userData.isLoggedIn;
    }
    
    // Función para cerrar sesión
    function logout() {
        MusicApp.removeFromLocalStorage('musicapp_user');
        window.location.href = '../index.html';
    }
    
    // Función para actualizar la interfaz según el estado de autenticación
    function updateAuthUI() {
        const isLoggedIn = checkAuth();
        const authButtons = document.querySelector('.auth-buttons');
        const loginPrompts = document.querySelectorAll('.login-prompt');
        
        if (isLoggedIn) {
            // Usuario autenticado
            if (authButtons) {
                const userData = MusicApp.getFromLocalStorage('musicapp_user');
                authButtons.innerHTML = `
                    <div class="user-menu">
                        <button class="user-menu-btn">
                            <span>${userData.name}</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown">
                            <a href="#"><i class="fas fa-user"></i> Mi perfil</a>
                            <a href="#"><i class="fas fa-cog"></i> Configuración</a>
                            <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Cerrar sesión</a>
                        </div>
                    </div>
                `;
                
                // Añadir evento al botón de logout
                const logoutBtn = document.getElementById('logout-btn');
                if (logoutBtn) {
                    logoutBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        logout();
                    });
                }
                
                // Toggle del menú de usuario
                const userMenuBtn = document.querySelector('.user-menu-btn');
                const userDropdown = document.querySelector('.user-dropdown');
                
                if (userMenuBtn && userDropdown) {
                    userMenuBtn.addEventListener('click', function() {
                        userDropdown.classList.toggle('active');
                    });
                    
                    // Cerrar al hacer clic fuera
                    document.addEventListener('click', function(e) {
                        if (!userMenuBtn.contains(e.target) && !userDropdown.contains(e.target)) {
                            userDropdown.classList.remove('active');
                        }
                    });
                }
            }
            
            // Ocultar prompts de login
            loginPrompts.forEach(prompt => {
                prompt.style.display = 'none';
            });
        }
    }
    
    // Actualizar UI al cargar la página
    updateAuthUI();
    
    // Exportar funciones de autenticación
    window.MusicAppAuth = {
        checkAuth,
        logout,
        updateAuthUI
    };
});
