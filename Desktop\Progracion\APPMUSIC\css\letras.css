/* Estilos para la página de letras - MusicApp */

.letras-container {
    padding: var(--spacing-xl) 0;
}

.login-prompt {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.login-message {
    max-width: 600px;
    margin: 0 auto;
}

.login-message i {
    font-size: 3rem;
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
}

.login-message h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
}

.login-message p {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-lg);
}

.login-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

.letras-tabs {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--color-border);
    padding-bottom: var(--spacing-md);
}

.tab-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: transparent;
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
}

.tab-btn:hover {
    color: var(--color-text);
}

.tab-btn.active {
    color: var(--color-primary);
    border-bottom-color: var(--color-primary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Editor de letras */
.editor-toolbar {
    display: flex;
    gap: var(--spacing-md);
    background-color: var(--color-background-light);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    margin-bottom: 1px;
    flex-wrap: wrap;
    justify-content: center;
}

.toolbar-group {
    display: flex;
    gap: var(--spacing-sm);
}

.toolbar-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--color-background);
    border-radius: var(--border-radius-sm);
    color: var(--color-text);
    transition: all var(--transition-fast);
}

.toolbar-btn:hover {
    background-color: var(--color-background-lighter);
}

.toolbar-btn i {
    margin-right: var(--spacing-xs);
}

.editor-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.song-info {
    grid-column: 1 / -1;
    background-color: var(--color-background-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    color: var(--color-text);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-primary);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.lyrics-editor {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.lyrics-editor textarea {
    width: 100%;
    height: 400px;
    padding: var(--spacing-lg);
    background-color: var(--color-background);
    border: none;
    color: var(--color-text);
    font-family: monospace;
    font-size: var(--font-size-md);
    line-height: 1.6;
    resize: none;
}

.lyrics-editor textarea:focus {
    outline: none;
}

.lyrics-preview {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    height: 400px;
    overflow-y: auto;
}

.lyrics-preview h3 {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
    color: var(--color-primary);
}

.preview-content {
    background-color: var(--color-background);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-family: monospace;
}

.preview-content h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xs);
}

.preview-content h3 {
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-md);
}

.lyrics-section {
    margin-bottom: var(--spacing-lg);
}

.section-title {
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--spacing-sm);
}

.lyrics-line {
    color: var(--color-text);
    margin-bottom: var(--spacing-xs);
}

.editor-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* Mi biblioteca */
.library-filters {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.search-bar {
    display: flex;
    flex: 1;
    max-width: 400px;
}

.search-bar input {
    flex: 1;
    padding: var(--spacing-md);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    color: var(--color-text);
}

.search-bar button {
    padding: 0 var(--spacing-md);
    background-color: var(--color-primary);
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
    color: var(--color-text);
}

.filter-options {
    display: flex;
    gap: var(--spacing-md);
}

.filter-select,
.sort-select {
    padding: var(--spacing-md);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    color: var(--color-text);
}

.library-empty {
    text-align: center;
    padding: var(--spacing-xxl) var(--spacing-lg);
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-xl);
}

.library-empty i {
    font-size: 3rem;
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-md);
}

.library-empty h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
}

.library-empty p {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-lg);
}

.empty-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

/* Proyectos */
.projects-section {
    background-color: var(--color-background-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.section-header h3 {
    font-size: var(--font-size-lg);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.projects-empty {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--color-text-secondary);
}

.projects-empty p {
    margin-bottom: var(--spacing-sm);
}

/* Media Queries */
@media screen and (max-width: 992px) {
    .editor-container {
        grid-template-columns: 1fr;
    }
}

@media screen and (max-width: 768px) {
    .letras-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        text-align: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .library-filters {
        flex-direction: column;
    }

    .search-bar {
        max-width: 100%;
    }

    .filter-options {
        width: 100%;
    }

    .filter-select,
    .sort-select {
        flex: 1;
    }
}

@media screen and (max-width: 480px) {
    .login-buttons {
        flex-direction: column;
    }

    .editor-toolbar {
        justify-content: center;
    }

    .empty-actions {
        flex-direction: column;
    }
}
