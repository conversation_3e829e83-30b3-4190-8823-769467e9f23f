// Servicio de datos de acordes simplificado para MusicApp
// Este archivo maneja solo datos locales para mejor rendimiento

// Datos de acordes (carga bajo demanda)
let chordData = null;
let chordTheory = null;
let loadingPromise = null;

// Función para cargar los datos de acordes de forma lazy
async function loadChordData() {
    // Si ya estamos cargando, esperar a que termine
    if (loadingPromise) {
        return loadingPromise;
    }

    // Si ya están cargados, devolver true
    if (chordData && chordTheory) {
        return true;
    }

    // Crear promesa de carga
    loadingPromise = (async () => {
        try {
            // Solo cargar datos de imágenes por ahora
            const imageResponse = await fetch('../data/chord-images.json');
            if (!imageResponse.ok) {
                throw new Error(`Error al cargar chord-images.json: ${imageResponse.status}`);
            }

            chordData = await imageResponse.json();

            // Datos de teoría básicos (sin cargar archivo adicional)
            chordTheory = {
                chordTypes: {
                    major: { theory: 'Acorde mayor (1-3-5)', feeling: '<PERSON>eg<PERSON>, estable' },
                    minor: { theory: 'Acorde menor (1-b3-5)', feeling: 'Melancólico, triste' }
                },
                scales: {
                    major: { name: 'Mayor', pattern: 'T-T-S-T-T-T-S' },
                    minor: { name: 'Menor', pattern: 'T-S-T-T-S-T-T' }
                }
            };

            console.log('Datos de acordes cargados correctamente');
            return true;
        } catch (error) {
            console.error('Error al cargar los datos de acordes:', error);
            loadingPromise = null; // Resetear para permitir reintentos
            return false;
        }
    })();

    return loadingPromise;
}

// Función para cargar los favoritos desde localStorage
function loadFavorites() {
    try {
        const favoritesJson = localStorage.getItem('musicAppFavorites');
        return favoritesJson ? JSON.parse(favoritesJson) : [];
    } catch (error) {
        console.error('Error al cargar favoritos:', error);
        return [];
    }
}

// Función para guardar favoritos en localStorage
function saveFavorites(favorites) {
    try {
        localStorage.setItem('musicAppFavorites', JSON.stringify(favorites));
        return true;
    } catch (error) {
        console.error('Error al guardar favoritos:', error);
        return false;
    }
}

// Servicio de datos de acordes simplificado
const chordDataService = {
    // Inicializar el servicio de forma lazy
    init: async function() {
        // Cargar favoritos inmediatamente (es rápido)
        this.favorites = loadFavorites();

        // Los datos de acordes se cargarán bajo demanda
        return true;
    },

    // Lista de favoritos
    favorites: [],

    // Obtener imagen de un acorde - simplificado
    getChordImage: function(instrument, root, type = 'major') {
        // Verificar si tenemos datos locales cargados
        if (chordData) {
            const normalizedType = this.normalizeChordType(type);

            if (chordData[instrument] &&
                chordData[instrument][root] &&
                chordData[instrument][root][normalizedType]) {

                return {
                    success: true,
                    data: chordData[instrument][root][normalizedType]
                };
            }
        }

        // Fallback: crear SVG simple
        const imageUrl = this.createSimpleSVG(root, type, instrument);

        return {
            success: true,
            data: {
                imageUrl: imageUrl,
                altText: `${root} ${type} en ${instrument}`,
                source: 'diagrama generado'
            }
        };
    },

    // Crear SVG simple como fallback
    createSimpleSVG: function(root, type, instrument) {
        const svg = `
            <svg width="250" height="150" xmlns="http://www.w3.org/2000/svg">
                <rect width="250" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                <text x="125" y="70" text-anchor="middle" font-family="Arial" font-size="20" font-weight="bold" fill="#495057">${root} ${type}</text>
                <text x="125" y="95" text-anchor="middle" font-family="Arial" font-size="14" fill="#6c757d">${instrument}</text>
                <text x="125" y="115" text-anchor="middle" font-family="Arial" font-size="12" fill="#868e96">Diagrama no disponible</text>
            </svg>
        `;

        return `data:image/svg+xml;base64,${btoa(svg)}`;
    },

    // Obtener información teórica de un acorde - simplificado
    getChordTheory: function(type = 'major') {
        // Cargar datos si no están disponibles
        if (!chordTheory) {
            loadChordData(); // Carga asíncrona, pero devolvemos datos básicos
        }

        const normalizedType = this.normalizeChordType(type);

        // Datos básicos siempre disponibles
        const basicTheory = {
            major: { theory: 'Acorde mayor (1-3-5)', feeling: 'Alegre, estable' },
            minor: { theory: 'Acorde menor (1-b3-5)', feeling: 'Melancólico, triste' },
            '7': { theory: 'Acorde dominante (1-3-5-b7)', feeling: 'Tensión, blues' }
        };

        const theory = (chordTheory && chordTheory.chordTypes && chordTheory.chordTypes[normalizedType])
                      || basicTheory[normalizedType]
                      || { theory: 'Información no disponible', feeling: 'Desconocido' };

        return {
            success: true,
            data: theory
        };
    },

    // Obtener información de escala - simplificado
    getScaleInfo: function(scale = 'major') {
        // Datos básicos siempre disponibles
        const basicScales = {
            major: { name: 'Mayor', pattern: 'T-T-S-T-T-T-S' },
            minor: { name: 'Menor', pattern: 'T-S-T-T-S-T-T' }
        };

        const scaleInfo = (chordTheory && chordTheory.scales && chordTheory.scales[scale])
                         || basicScales[scale]
                         || { name: 'Desconocida', pattern: 'No disponible' };

        return {
            success: true,
            data: scaleInfo
        };
    },

    // Normalizar el tipo de acorde
    normalizeChordType: function(type) {
        const typeMap = {
            'major': 'major',
            'minor': 'minor',
            'm': 'minor',
            'dim': 'dim',
            'diminished': 'dim',
            'aug': 'aug',
            'augmented': 'aug',
            '7': '7',
            'dominant7': '7',
            'maj7': 'maj7',
            'major7': 'maj7',
            'min7': 'min7',
            'minor7': 'min7',
            'm7': 'min7',
            'sus2': 'sus2',
            'sus4': 'sus4',
            '9': '9',
            'add9': 'add9'
        };

        return typeMap[type.toLowerCase()] || type;
    },

    // Buscar acordes - simplificado
    searchChords: function(query) {
        const results = [];
        const queryLower = query.toLowerCase();

        // Si no hay datos cargados, usar búsqueda básica
        if (!chordData) {
            const basicChords = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];
            const basicTypes = ['major', 'minor'];

            for (const root of basicChords) {
                for (const type of basicTypes) {
                    const chordName = `${root} ${type}`;
                    if (chordName.toLowerCase().includes(queryLower)) {
                        results.push({
                            id: `guitar-${root}-${type}`,
                            instrument: 'guitar',
                            root,
                            type,
                            name: chordName,
                            imageUrl: null,
                            isFavorite: this.isFavorite(`guitar-${root}-${type}`)
                        });
                    }
                }
            }
        } else {
            // Buscar en datos cargados
            for (const instrument in chordData) {
                for (const root in chordData[instrument]) {
                    for (const type in chordData[instrument][root]) {
                        const chordName = `${root} ${type}`;
                        if (chordName.toLowerCase().includes(queryLower)) {
                            const chordId = `${instrument}-${root}-${type}`;
                            results.push({
                                id: chordId,
                                instrument,
                                root,
                                type,
                                name: chordName,
                                imageUrl: chordData[instrument][root][type].imageUrl,
                                isFavorite: this.isFavorite(chordId)
                            });
                        }
                    }
                }
            }
        }

        return {
            success: true,
            count: results.length,
            data: results
        };
    },

    // Verificar si un acorde es favorito
    isFavorite: function(chordId) {
        return this.favorites.includes(chordId);
    },

    // Añadir un acorde a favoritos
    addFavorite: function(instrument, root, type) {
        const chordId = `${instrument}-${root}-${type}`;

        if (!this.isFavorite(chordId)) {
            this.favorites.push(chordId);
            saveFavorites(this.favorites);
        }

        return {
            success: true,
            isFavorite: true,
            message: `Acorde ${root} ${type} añadido a favoritos`
        };
    },

    // Eliminar un acorde de favoritos
    removeFavorite: function(instrument, root, type) {
        const chordId = `${instrument}-${root}-${type}`;

        if (this.isFavorite(chordId)) {
            this.favorites = this.favorites.filter(id => id !== chordId);
            saveFavorites(this.favorites);
        }

        return {
            success: true,
            isFavorite: false,
            message: `Acorde ${root} ${type} eliminado de favoritos`
        };
    },

    // Alternar estado de favorito
    toggleFavorite: function(instrument, root, type) {
        const chordId = `${instrument}-${root}-${type}`;

        if (this.isFavorite(chordId)) {
            return this.removeFavorite(instrument, root, type);
        } else {
            return this.addFavorite(instrument, root, type);
        }
    },

    // Obtener todos los acordes favoritos - simplificado
    getFavorites: function() {
        const favorites = [];

        // Recorrer la lista de favoritos
        for (const chordId of this.favorites) {
            const [instrument, root, type] = chordId.split('-');

            // Crear objeto de favorito básico
            const favorite = {
                id: chordId,
                instrument,
                root,
                type,
                name: `${root} ${type}`,
                imageUrl: null,
                isFavorite: true
            };

            // Si tenemos datos cargados, usar la imagen real
            if (chordData &&
                chordData[instrument] &&
                chordData[instrument][root] &&
                chordData[instrument][root][type]) {
                favorite.imageUrl = chordData[instrument][root][type].imageUrl;
            }

            favorites.push(favorite);
        }

        return {
            success: true,
            count: favorites.length,
            data: favorites
        };
    }
};

// Exportar el servicio para uso en otros archivos
window.ChordDataService = chordDataService;
