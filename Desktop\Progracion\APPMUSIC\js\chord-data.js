// Servicio de datos de acordes para MusicApp
// Este archivo maneja la carga y acceso a los datos locales de acordes

// Datos de acordes (se cargarán desde los JSON)
let chordData = null;
let chordTheory = null;

// Función para cargar los datos de acordes desde el JSON local
async function loadChordData() {
    try {
        // Cargar datos de imágenes de acordes
        const imageResponse = await fetch('../data/chord-images.json');
        if (!imageResponse.ok) {
            throw new Error(`Error al cargar los datos de imágenes de acordes: ${imageResponse.status}`);
        }

        chordData = await imageResponse.json();
        console.log('Datos de imágenes de acordes cargados correctamente');

        // Cargar datos de teoría de acordes
        const theoryResponse = await fetch('../data/chord-theory.json');
        if (!theoryResponse.ok) {
            throw new Error(`Error al cargar los datos de teoría de acordes: ${theoryResponse.status}`);
        }

        chordTheory = await theoryResponse.json();
        console.log('Datos de teoría de acordes cargados correctamente');

        return true;
    } catch (error) {
        console.error('Error al cargar los datos de acordes:', error);
        return false;
    }
}

// Función para cargar los favoritos desde localStorage
function loadFavorites() {
    try {
        const favoritesJson = localStorage.getItem('musicAppFavorites');
        return favoritesJson ? JSON.parse(favoritesJson) : [];
    } catch (error) {
        console.error('Error al cargar favoritos:', error);
        return [];
    }
}

// Función para guardar favoritos en localStorage
function saveFavorites(favorites) {
    try {
        localStorage.setItem('musicAppFavorites', JSON.stringify(favorites));
        return true;
    } catch (error) {
        console.error('Error al guardar favoritos:', error);
        return false;
    }
}

// Servicio de datos de acordes
const chordDataService = {
    // Inicializar el servicio
    init: async function() {
        // Cargar datos de acordes
        const success = await loadChordData();

        // Cargar favoritos
        this.favorites = loadFavorites();

        return success;
    },

    // Lista de favoritos
    favorites: [],

    // Obtener imagen de un acorde
    getChordImage: function(instrument, root, type = 'major') {
        // Verificar si los datos están cargados
        if (!chordData) {
            console.error('Los datos de acordes no están cargados');
            return {
                success: false,
                error: 'Los datos de acordes no están cargados'
            };
        }

        // Normalizar el tipo de acorde
        const normalizedType = this.normalizeChordType(type);

        // Verificar si tenemos datos para este acorde
        if (chordData[instrument] &&
            chordData[instrument][root] &&
            chordData[instrument][root][normalizedType]) {

            return {
                success: true,
                data: chordData[instrument][root][normalizedType]
            };
        }

        // Si no tenemos datos específicos, intentar generar una URL
        const normalizedRoot = root.replace('#', 'sharp').replace('b', 'flat');

        // Construir una URL genérica
        const imageUrl = `https://www.scales-chords.com/chord-charts/${instrument}-${normalizedRoot}-${normalizedType}.png`;

        return {
            success: true,
            data: {
                imageUrl: imageUrl,
                altText: `${root} ${type} en ${instrument}`,
                source: 'scales-chords.com (generado)'
            }
        };
    },

    // Obtener información teórica de un acorde
    getChordTheory: function(type = 'major') {
        // Verificar si los datos están cargados
        if (!chordTheory) {
            console.error('Los datos de teoría de acordes no están cargados');
            return {
                success: false,
                error: 'Los datos de teoría de acordes no están cargados'
            };
        }

        // Normalizar el tipo de acorde
        const normalizedType = this.normalizeChordType(type);

        // Verificar si tenemos datos para este tipo de acorde
        if (chordTheory.chordTypes && chordTheory.chordTypes[normalizedType]) {
            return {
                success: true,
                data: chordTheory.chordTypes[normalizedType]
            };
        }

        // Si no tenemos datos específicos, devolver un error
        return {
            success: false,
            error: `No hay información teórica disponible para el tipo de acorde ${type}`
        };
    },

    // Obtener información de escala
    getScaleInfo: function(scale = 'major') {
        // Verificar si los datos están cargados
        if (!chordTheory) {
            console.error('Los datos de teoría de acordes no están cargados');
            return {
                success: false,
                error: 'Los datos de teoría de acordes no están cargados'
            };
        }

        // Verificar si tenemos datos para esta escala
        if (chordTheory.scales && chordTheory.scales[scale]) {
            return {
                success: true,
                data: chordTheory.scales[scale]
            };
        }

        // Si no tenemos datos específicos, devolver un error
        return {
            success: false,
            error: `No hay información disponible para la escala ${scale}`
        };
    },

    // Normalizar el tipo de acorde
    normalizeChordType: function(type) {
        const typeMap = {
            'major': 'major',
            'minor': 'minor',
            'm': 'minor',
            'dim': 'dim',
            'diminished': 'dim',
            'aug': 'aug',
            'augmented': 'aug',
            '7': '7',
            'dominant7': '7',
            'maj7': 'maj7',
            'major7': 'maj7',
            'min7': 'min7',
            'minor7': 'min7',
            'm7': 'min7',
            'sus2': 'sus2',
            'sus4': 'sus4',
            '9': '9',
            'add9': 'add9'
        };

        return typeMap[type.toLowerCase()] || type;
    },

    // Buscar acordes
    searchChords: function(query) {
        // Verificar si los datos están cargados
        if (!chordData) {
            console.error('Los datos de acordes no están cargados');
            return {
                success: false,
                error: 'Los datos de acordes no están cargados'
            };
        }

        const results = [];
        const queryLower = query.toLowerCase();

        // Buscar en todos los instrumentos
        for (const instrument in chordData) {
            for (const root in chordData[instrument]) {
                for (const type in chordData[instrument][root]) {
                    // Crear el nombre del acorde
                    const chordName = `${root} ${type}`;

                    // Verificar si el nombre del acorde contiene la consulta
                    if (chordName.toLowerCase().includes(queryLower) ||
                        root.toLowerCase().includes(queryLower) ||
                        type.toLowerCase().includes(queryLower)) {

                        // Crear un ID único para el acorde
                        const chordId = `${instrument}-${root}-${type}`;

                        results.push({
                            id: chordId,
                            instrument,
                            root,
                            type,
                            name: chordName,
                            imageUrl: chordData[instrument][root][type].imageUrl,
                            isFavorite: this.isFavorite(chordId)
                        });
                    }
                }
            }
        }

        return {
            success: true,
            count: results.length,
            data: results
        };
    },

    // Verificar si un acorde es favorito
    isFavorite: function(chordId) {
        return this.favorites.includes(chordId);
    },

    // Añadir un acorde a favoritos
    addFavorite: function(instrument, root, type) {
        const chordId = `${instrument}-${root}-${type}`;

        if (!this.isFavorite(chordId)) {
            this.favorites.push(chordId);
            saveFavorites(this.favorites);
        }

        return {
            success: true,
            isFavorite: true,
            message: `Acorde ${root} ${type} añadido a favoritos`
        };
    },

    // Eliminar un acorde de favoritos
    removeFavorite: function(instrument, root, type) {
        const chordId = `${instrument}-${root}-${type}`;

        if (this.isFavorite(chordId)) {
            this.favorites = this.favorites.filter(id => id !== chordId);
            saveFavorites(this.favorites);
        }

        return {
            success: true,
            isFavorite: false,
            message: `Acorde ${root} ${type} eliminado de favoritos`
        };
    },

    // Alternar estado de favorito
    toggleFavorite: function(instrument, root, type) {
        const chordId = `${instrument}-${root}-${type}`;

        if (this.isFavorite(chordId)) {
            return this.removeFavorite(instrument, root, type);
        } else {
            return this.addFavorite(instrument, root, type);
        }
    },

    // Obtener todos los acordes favoritos
    getFavorites: function() {
        // Verificar si los datos están cargados
        if (!chordData) {
            console.error('Los datos de acordes no están cargados');
            return {
                success: false,
                error: 'Los datos de acordes no están cargados'
            };
        }

        const favorites = [];

        // Recorrer la lista de favoritos
        for (const chordId of this.favorites) {
            const [instrument, root, type] = chordId.split('-');

            // Verificar si tenemos datos para este acorde
            if (chordData[instrument] &&
                chordData[instrument][root] &&
                chordData[instrument][root][type]) {

                favorites.push({
                    id: chordId,
                    instrument,
                    root,
                    type,
                    name: `${root} ${type}`,
                    imageUrl: chordData[instrument][root][type].imageUrl,
                    isFavorite: true
                });
            }
        }

        return {
            success: true,
            count: favorites.length,
            data: favorites
        };
    }
};

// Exportar el servicio para uso en otros archivos
window.ChordDataService = chordDataService;
