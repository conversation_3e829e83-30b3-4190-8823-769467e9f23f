<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Escalas - MusicApp</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/escalas.css">
    <!-- Font Awesome para iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1><a href="../index.html">MusicApp</a></h1>
            </div>
            <ul class="nav-links">
                <li><a href="../index.html">Inicio</a></li>
                <li><a href="acordes.html">Acordes</a></li>
                <li><a href="escalas.html" class="active">Escalas</a></li>
                <li><a href="cifrados.html">Cifrados</a></li>
                <li><a href="letras.html">Letras</a></li>
            </ul>
            <div class="auth-buttons">
                <a href="login.html" class="btn btn-login">Iniciar Sesión</a>
                <a href="register.html" class="btn btn-register">Registrarse</a>
            </div>
            <div class="burger">
                <div class="line1"></div>
                <div class="line2"></div>
                <div class="line3"></div>
            </div>
        </nav>
    </header>

    <main>
        <section class="page-header">
            <div class="container">
                <h1>Escalas</h1>
                <p>Aprende escalas musicales en diferentes tonalidades e instrumentos</p>
            </div>
        </section>

        <section class="escalas-container">
            <div class="container">
                <div class="instrument-selector">
                    <button class="btn btn-instrument active" data-instrument="guitar">
                        <i class="fas fa-guitar"></i> Guitarra
                    </button>
                    <button class="btn btn-instrument" data-instrument="piano">
                        <i class="fas fa-music"></i> Piano
                    </button>
                    <button class="btn btn-instrument" data-instrument="bass">
                        <i class="fas fa-music"></i> Bajo
                    </button>
                </div>

                <div class="scale-controls">
                    <div class="scale-selector">
                        <label for="root-note">Nota raíz:</label>
                        <select id="root-note" class="scale-select">
                            <option value="C">C (Do)</option>
                            <option value="C#">C# (Do#)</option>
                            <option value="D">D (Re)</option>
                            <option value="D#">D# (Re#)</option>
                            <option value="E">E (Mi)</option>
                            <option value="F">F (Fa)</option>
                            <option value="F#">F# (Fa#)</option>
                            <option value="G">G (Sol)</option>
                            <option value="G#">G# (Sol#)</option>
                            <option value="A">A (La)</option>
                            <option value="A#">A# (La#)</option>
                            <option value="B">B (Si)</option>
                        </select>
                    </div>

                    <div class="scale-selector">
                        <label for="scale-type">Tipo de escala:</label>
                        <select id="scale-type" class="scale-select">
                            <option value="major">Mayor</option>
                            <option value="minor">Menor natural</option>
                            <option value="harmonic_minor">Menor armónica</option>
                            <option value="melodic_minor">Menor melódica</option>
                            <option value="pentatonic_major">Pentatónica mayor</option>
                            <option value="pentatonic_minor">Pentatónica menor</option>
                            <option value="blues">Blues</option>
                            <option value="dorian">Dórica</option>
                            <option value="phrygian">Frigia</option>
                            <option value="lydian">Lidia</option>
                            <option value="mixolydian">Mixolidia</option>
                            <option value="locrian">Locria</option>
                        </select>
                    </div>
                </div>

                <div class="scale-display">
                    <div class="scale-name">C Mayor</div>
                    
                    <!-- Visualizador de Guitarra (visible por defecto) -->
                    <div class="scale-diagram guitar-diagram active">
                        <div class="guitar-neck">
                            <div class="guitar-strings">
                                <div class="guitar-string"></div>
                                <div class="guitar-string"></div>
                                <div class="guitar-string"></div>
                                <div class="guitar-string"></div>
                                <div class="guitar-string"></div>
                                <div class="guitar-string"></div>
                            </div>
                            <div class="guitar-frets">
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                                <div class="guitar-fret"></div>
                            </div>
                            <div class="scale-notes">
                                <!-- Aquí se generarán las notas de la escala dinámicamente con JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Visualizador de Piano (oculto por defecto) -->
                    <div class="scale-diagram piano-diagram">
                        <div class="piano-keyboard">
                            <div class="piano-keys">
                                <div class="piano-key white" data-note="C"></div>
                                <div class="piano-key black" data-note="C#"></div>
                                <div class="piano-key white" data-note="D"></div>
                                <div class="piano-key black" data-note="D#"></div>
                                <div class="piano-key white" data-note="E"></div>
                                <div class="piano-key white" data-note="F"></div>
                                <div class="piano-key black" data-note="F#"></div>
                                <div class="piano-key white" data-note="G"></div>
                                <div class="piano-key black" data-note="G#"></div>
                                <div class="piano-key white" data-note="A"></div>
                                <div class="piano-key black" data-note="A#"></div>
                                <div class="piano-key white" data-note="B"></div>
                                <div class="piano-key white" data-note="C2"></div>
                                <div class="piano-key black" data-note="C#2"></div>
                                <div class="piano-key white" data-note="D2"></div>
                                <div class="piano-key black" data-note="D#2"></div>
                                <div class="piano-key white" data-note="E2"></div>
                                <div class="piano-key white" data-note="F2"></div>
                                <div class="piano-key black" data-note="F#2"></div>
                                <div class="piano-key white" data-note="G2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Visualizador de Bajo (oculto por defecto) -->
                    <div class="scale-diagram bass-diagram">
                        <div class="bass-neck">
                            <div class="bass-strings">
                                <div class="bass-string"></div>
                                <div class="bass-string"></div>
                                <div class="bass-string"></div>
                                <div class="bass-string"></div>
                            </div>
                            <div class="bass-frets">
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                                <div class="bass-fret"></div>
                            </div>
                            <div class="scale-notes">
                                <!-- Aquí se generarán las notas de la escala dinámicamente con JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="scale-info">
                    <div class="scale-notes-list">
                        <h3>Notas de la escala</h3>
                        <div class="notes-container">
                            <span class="scale-note root">C</span>
                            <span class="scale-note">D</span>
                            <span class="scale-note">E</span>
                            <span class="scale-note">F</span>
                            <span class="scale-note">G</span>
                            <span class="scale-note">A</span>
                            <span class="scale-note">B</span>
                        </div>
                    </div>
                    <div class="scale-formula">
                        <h3>Fórmula</h3>
                        <p>T - T - S - T - T - T - S</p>
                        <p class="formula-explanation">T = Tono, S = Semitono</p>
                    </div>
                </div>

                <div class="scale-positions">
                    <h3>Posiciones en el diapasón</h3>
                    <div class="positions-grid">
                        <button class="position-btn active" data-position="1">Pos 1</button>
                        <button class="position-btn" data-position="2">Pos 2</button>
                        <button class="position-btn" data-position="3">Pos 3</button>
                        <button class="position-btn" data-position="4">Pos 4</button>
                        <button class="position-btn" data-position="5">Pos 5</button>
                        <button class="position-btn" data-position="6">Pos 6</button>
                        <button class="position-btn" data-position="7">Pos 7</button>
                    </div>
                </div>
            </div>
        </section>

        <section class="related-scales">
            <div class="container">
                <h3>Escalas relacionadas</h3>
                <div class="related-scales-grid">
                    <a href="#" class="related-scale" data-scale="A_minor">A menor (relativa)</a>
                    <a href="#" class="related-scale" data-scale="G_major">G mayor (dominante)</a>
                    <a href="#" class="related-scale" data-scale="F_major">F mayor (subdominante)</a>
                    <a href="#" class="related-scale" data-scale="C_pentatonic">C pentatónica</a>
                    <a href="#" class="related-scale" data-scale="C_blues">C blues</a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <h2>MusicApp</h2>
                <p>Tu plataforma musical</p>
            </div>
            <div class="footer-links">
                <h3>Enlaces rápidos</h3>
                <ul>
                    <li><a href="../index.html">Inicio</a></li>
                    <li><a href="acordes.html">Acordes</a></li>
                    <li><a href="escalas.html">Escalas</a></li>
                    <li><a href="cifrados.html">Cifrados</a></li>
                    <li><a href="letras.html">Letras</a></li>
                </ul>
            </div>
            <div class="footer-contact">
                <h3>Contacto</h3>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-phone"></i> +************</p>
                <div class="social-icons">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 MusicApp. Todos los derechos reservados.</p>
        </div>
    </footer>

    <script src="../js/main.js"></script>
    <script src="../js/escalas.js"></script>
</body>
</html>
