// JavaScript para la página de acordes - MusicApp

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar el servicio de datos de acordes de forma lazy
    ChordDataService.init();

    // Elementos de la interfaz
    const instrumentButtons = document.querySelectorAll('.btn-instrument');
    const rootNoteSelect = document.getElementById('root-note');
    const chordTypeSelect = document.getElementById('chord-type');
    const chordNameElement = document.querySelector('.chord-name');
    const variationCards = document.querySelectorAll('.variation-card');
    const relatedChords = document.querySelectorAll('.related-chord');

    // Elementos para la búsqueda
    const searchInput = document.getElementById('chord-search-input');
    const searchButton = document.getElementById('chord-search-btn');
    const searchResultsModal = document.getElementById('search-results-modal');
    const searchResultsContainer = document.getElementById('search-results-container');
    const closeModalButton = document.querySelector('.close-modal');

    // Elementos para las posiciones del acorde
    const chordPositionsContainer = document.querySelector('.chord-positions-text');
    const guitarPositions = document.querySelector('.guitar-chord-positions');
    const pianoPositions = document.querySelector('.piano-chord-positions');
    const bassPositions = document.querySelector('.bass-chord-positions');

    // Elementos para favoritos
    const favoriteButton = document.getElementById('favorite-btn');
    const favoritesGrid = document.querySelector('.favorites-grid');

    // Elementos para la teoría musical
    const theoryTabs = document.querySelectorAll('.theory-tab');
    const theoryTabContents = document.querySelectorAll('.theory-tab-content');
    const chordStructure = document.querySelector('.chord-structure');
    const chordFeeling = document.querySelector('.chord-feeling');
    const chordUses = document.querySelector('.chord-uses');
    const chordExamples = document.querySelector('.chord-examples');
    const scaleName = document.querySelector('.scale-name');
    const scalePattern = document.querySelector('.scale-pattern');
    const scaleDegrees = document.querySelector('.scale-degrees');
    const scaleChords = document.querySelector('.scale-chords');

    // Datos de acordes (simplificados para este ejemplo)
    const chordData = {
        guitar: {
            C: {
                major: {
                    positions: [
                        {
                            name: 'Posición 1',
                            dots: [
                                { string: 5, fret: 3 },
                                { string: 4, fret: 2 },
                                { string: 2, fret: 1 }
                            ],
                            info: {
                                position: 'Abierta',
                                fingers: '1 (1er traste, 2da cuerda), 2 (2do traste, 4ta cuerda), 3 (3er traste, 5ta cuerda)'
                            }
                        },
                        {
                            name: 'Posición 2',
                            dots: [
                                { string: 6, fret: 8 },
                                { string: 5, fret: 10 },
                                { string: 4, fret: 10 },
                                { string: 3, fret: 9 },
                                { string: 2, fret: 8 },
                                { string: 1, fret: 8 }
                            ],
                            info: {
                                position: 'Traste 8',
                                fingers: 'Barra en traste 8, 3 (10mo traste, 5ta cuerda), 4 (10mo traste, 4ta cuerda), 2 (9no traste, 3ra cuerda)'
                            }
                        }
                    ],
                    related: ['Am', 'F', 'G', 'Em', 'Dm']
                },
                minor: {
                    positions: [
                        {
                            name: 'Posición 1',
                            dots: [
                                { string: 5, fret: 3 },
                                { string: 4, fret: 1 },
                                { string: 2, fret: 1 }
                            ],
                            info: {
                                position: 'Abierta',
                                fingers: '1 (1er traste, 4ta cuerda), 2 (1er traste, 2da cuerda), 3 (3er traste, 5ta cuerda)'
                            }
                        }
                    ],
                    related: ['Eb', 'Ab', 'G', 'Fm', 'Gm']
                }
            },
            G: {
                major: {
                    positions: [
                        {
                            name: 'Posición 1',
                            dots: [
                                { string: 6, fret: 3 },
                                { string: 5, fret: 2 },
                                { string: 1, fret: 3 }
                            ],
                            info: {
                                position: 'Abierta',
                                fingers: '1 (2do traste, 5ta cuerda), 2 (3er traste, 6ta cuerda), 3 (3er traste, 1ra cuerda)'
                            }
                        }
                    ],
                    related: ['Em', 'C', 'D', 'Am', 'Bm']
                }
            }
        },
        piano: {
            C: {
                major: {
                    notes: ['C', 'E', 'G'],
                    keys: ['C', 'E', 'G'],
                    info: {
                        notes: 'C, E, G',
                        structure: '1-3-5'
                    }
                },
                minor: {
                    notes: ['C', 'Eb', 'G'],
                    keys: ['C', 'D#', 'G'],
                    info: {
                        notes: 'C, Eb, G',
                        structure: '1-b3-5'
                    }
                }
            }
        },
        bass: {
            C: {
                major: {
                    positions: [
                        {
                            name: 'Posición 1',
                            dots: [
                                { string: 3, fret: 3 },
                                { string: 2, fret: 5 },
                                { string: 1, fret: 3 }
                            ],
                            info: {
                                notes: 'C, G, C (octava)',
                                position: '3er traste, 3ra cuerda (C)'
                            }
                        }
                    ]
                }
            }
        }
    };

    // Instrumento actual
    let currentInstrument = 'guitar';
    let currentRoot = 'C';
    let currentType = 'major';

    // Función para cambiar el instrumento
    function changeInstrument(instrument) {
        currentInstrument = instrument;

        // Actualizar botones
        instrumentButtons.forEach(button => {
            button.classList.remove('active');
            if (button.getAttribute('data-instrument') === instrument) {
                button.classList.add('active');
            }
        });

        // Actualizar las posiciones mostradas
        updateInstrumentPositions(instrument);

        // Actualizar el acorde mostrado
        updateChordDisplay();
    }

    // Función para actualizar las posiciones del instrumento
    function updateInstrumentPositions(instrument) {
        // Ocultar todas las posiciones
        if (guitarPositions) guitarPositions.classList.remove('active');
        if (pianoPositions) pianoPositions.classList.remove('active');
        if (bassPositions) bassPositions.classList.remove('active');

        // Mostrar la posición del instrumento seleccionado
        switch(instrument) {
            case 'guitar':
                if (guitarPositions) guitarPositions.classList.add('active');
                break;
            case 'piano':
                if (pianoPositions) pianoPositions.classList.add('active');
                break;
            case 'bass':
                if (bassPositions) bassPositions.classList.add('active');
                break;
        }
    }

    // Función para actualizar la visualización del acorde
    function updateChordDisplay() {
        // Actualizar el nombre del acorde
        if (chordNameElement) {
            chordNameElement.textContent = `${currentRoot} ${getChordTypeName(currentType)}`;
        }

        // Actualizar acordes relacionados
        updateRelatedChords();

        // Obtener información detallada del acorde desde la API
        updateChordDetails();

        // Actualizar las posiciones del acorde
        await updateChordPositions();

        // Actualizar el estado del botón de favoritos
        updateFavoriteButton();

        // Actualizar la información teórica
        updateTheoryInfo();
    }

    // Función para actualizar el estado del botón de favoritos
    function updateFavoriteButton() {
        if (!favoriteButton) return;

        const isFavorite = ChordDataService.isFavorite(`${currentInstrument}-${currentRoot}-${currentType}`);

        // Actualizar el icono y la clase
        if (isFavorite) {
            favoriteButton.innerHTML = '<i class="fas fa-heart"></i>';
            favoriteButton.classList.add('active');
            favoriteButton.title = 'Eliminar de favoritos';
        } else {
            favoriteButton.innerHTML = '<i class="far fa-heart"></i>';
            favoriteButton.classList.remove('active');
            favoriteButton.title = 'Añadir a favoritos';
        }
    }

    // Función para actualizar la información teórica
    function updateTheoryInfo() {
        // Obtener información teórica del tipo de acorde
        const chordTheoryResponse = ChordDataService.getChordTheory(currentType);

        if (chordTheoryResponse.success) {
            const theory = chordTheoryResponse.data;

            // Actualizar la estructura del acorde
            if (chordStructure) {
                chordStructure.textContent = theory.theory || '-';
            }

            // Actualizar las características sonoras
            if (chordFeeling) {
                chordFeeling.textContent = theory.feeling || '-';
            }

            // Actualizar los usos comunes
            if (chordUses) {
                chordUses.innerHTML = '';
                if (theory.commonUses && theory.commonUses.length > 0) {
                    theory.commonUses.forEach(use => {
                        const li = document.createElement('li');
                        li.textContent = use;
                        chordUses.appendChild(li);
                    });
                } else {
                    const li = document.createElement('li');
                    li.textContent = 'No hay información disponible.';
                    chordUses.appendChild(li);
                }
            }

            // Actualizar los ejemplos
            if (chordExamples) {
                chordExamples.textContent = theory.examples ? theory.examples.join(', ') : '-';
            }
        } else {
            // Si no hay información disponible, mostrar mensaje
            if (chordStructure) chordStructure.textContent = 'No hay información disponible.';
            if (chordFeeling) chordFeeling.textContent = 'No hay información disponible.';
            if (chordUses) chordUses.innerHTML = '<li>No hay información disponible.</li>';
            if (chordExamples) chordExamples.textContent = 'No hay información disponible.';
        }

        // Obtener información de la escala relacionada
        const scaleType = currentType === 'major' || currentType === 'maj7' ? 'major' : 'minor';
        const scaleResponse = ChordDataService.getScaleInfo(scaleType);

        if (scaleResponse.success) {
            const scale = scaleResponse.data;

            // Actualizar el nombre de la escala
            if (scaleName) {
                scaleName.textContent = `${currentRoot} ${scale.name}`;
            }

            // Actualizar el patrón de intervalos
            if (scalePattern) {
                scalePattern.textContent = scale.pattern || '-';
            }

            // Actualizar los grados de la escala
            if (scaleDegrees) {
                scaleDegrees.textContent = scale.degrees ? scale.degrees.join(', ') : '-';
            }

            // Actualizar los acordes de la escala
            if (scaleChords) {
                scaleChords.textContent = scale.chords ? scale.chords.join(', ') : '-';
            }
        } else {
            // Si no hay información disponible, mostrar mensaje
            if (scaleName) scaleName.textContent = 'No hay información disponible.';
            if (scalePattern) scalePattern.textContent = 'No hay información disponible.';
            if (scaleDegrees) scaleDegrees.textContent = 'No hay información disponible.';
            if (scaleChords) scaleChords.textContent = 'No hay información disponible.';
        }
    }

    // Función para actualizar la lista de favoritos
    function updateFavorites() {
        if (!favoritesGrid) return;

        // Obtener los favoritos
        const favoritesResponse = ChordDataService.getFavorites();

        if (favoritesResponse.success && favoritesResponse.data.length > 0) {
            // Limpiar el contenedor
            favoritesGrid.innerHTML = '';

            // Mostrar los favoritos
            favoritesResponse.data.forEach(favorite => {
                const card = document.createElement('div');
                card.className = 'favorite-card';
                card.innerHTML = `
                    <div class="favorite-image">
                        <img src="${favorite.imageUrl}" alt="${favorite.name}" loading="lazy">
                    </div>
                    <div class="favorite-info">
                        <div class="favorite-name">${favorite.name}</div>
                        <div class="favorite-instrument">${getInstrumentName(favorite.instrument)}</div>
                    </div>
                    <button class="remove-favorite" title="Eliminar de favoritos" data-id="${favorite.id}">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                // Añadir evento para seleccionar el acorde
                card.addEventListener('click', function(e) {
                    // Evitar que se active si se hizo clic en el botón de eliminar
                    if (e.target.closest('.remove-favorite')) return;

                    // Cambiar al instrumento correspondiente
                    const instrumentButton = document.querySelector(`.btn-instrument[data-instrument="${favorite.instrument}"]`);
                    if (instrumentButton) {
                        instrumentButton.click();
                    }

                    // Seleccionar el acorde
                    if (rootNoteSelect) {
                        rootNoteSelect.value = favorite.root;
                    }

                    if (chordTypeSelect) {
                        chordTypeSelect.value = favorite.type;
                    }

                    // Actualizar la visualización
                    currentRoot = favorite.root;
                    currentType = favorite.type;
                    updateChordDisplay();
                });

                // Añadir evento para eliminar de favoritos
                const removeButton = card.querySelector('.remove-favorite');
                if (removeButton) {
                    removeButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const chordId = this.getAttribute('data-id');
                        const [instrument, root, type] = chordId.split('-');

                        // Eliminar de favoritos
                        ChordDataService.removeFavorite(instrument, root, type);

                        // Actualizar la lista de favoritos
                        updateFavorites();

                        // Actualizar el botón de favoritos si es el acorde actual
                        if (currentInstrument === instrument && currentRoot === root && currentType === type) {
                            updateFavoriteButton();
                        }

                        // Mostrar mensaje
                        MusicApp.showAlert(`Acorde ${root} ${type} eliminado de favoritos`, 'success');
                    });
                }

                favoritesGrid.appendChild(card);
            });
        } else {
            // Mostrar mensaje de que no hay favoritos
            favoritesGrid.innerHTML = '<p class="no-favorites">No tienes acordes favoritos. Marca algunos acordes como favoritos haciendo clic en el corazón.</p>';
        }
    }

    // Función para actualizar las posiciones del acorde
    async function updateChordPositions() {
        try {
            // Obtener las posiciones del acorde desde el servicio de datos
            const response = await ChordDataService.getChordImage(currentInstrument, currentRoot, currentType);

            if (response.success && response.data) {
                // Actualizar las posiciones según el instrumento
                switch(currentInstrument) {
                    case 'guitar':
                        updateGuitarPositions(response.data);
                        break;
                    case 'piano':
                        updatePianoPositions(response.data);
                        break;
                    case 'bass':
                        updateBassPositions(response.data);
                        break;
                }

                // Mostrar la imagen si está disponible
                if (response.data.imageUrl) {
                    showChordImage(response.data);
                }
            } else {
                console.error('Error al obtener datos del acorde:', response.error);
            }
        } catch (error) {
            console.error('Error al actualizar posiciones del acorde:', error);
        }
    }

    // Función para mostrar la imagen del acorde
    function showChordImage(chordData) {
        // Buscar contenedor de imagen
        const imageContainer = document.querySelector('.chord-positions-container');
        if (!imageContainer) return;

        // Crear o actualizar imagen
        let imageElement = imageContainer.querySelector('.chord-image');
        if (!imageElement) {
            imageElement = document.createElement('img');
            imageElement.className = 'chord-image';
            imageElement.style.maxWidth = '100%';
            imageElement.style.marginTop = '15px';
            imageElement.style.borderRadius = '8px';
            imageContainer.appendChild(imageElement);
        }

        imageElement.src = chordData.imageUrl;
        imageElement.alt = chordData.altText || `${currentRoot} ${currentType} en ${currentInstrument}`;

        // Manejar errores de carga
        imageElement.onerror = function() {
            this.style.display = 'none';
        };

        imageElement.onload = function() {
            this.style.display = 'block';
        };
    }

    // Función para obtener las posiciones de un acorde
    function getChordPositions(instrument, root, type) {
        // Base de datos simplificada de posiciones
        const positions = {
            guitar: {
                C: {
                    major: {
                        name: 'Posición abierta',
                        positions: [
                            'Cuerda 1 (Mi): Abierta',
                            'Cuerda 2 (Si): Traste 1 (dedo 1)',
                            'Cuerda 3 (Sol): Abierta',
                            'Cuerda 4 (Re): Traste 2 (dedo 2)',
                            'Cuerda 5 (La): Traste 3 (dedo 3)',
                            'Cuerda 6 (Mi): No tocar (X)'
                        ]
                    },
                    minor: {
                        name: 'Posición abierta',
                        positions: [
                            'Cuerda 1 (Mi): Abierta',
                            'Cuerda 2 (Si): Traste 1 (dedo 1)',
                            'Cuerda 3 (Sol): Abierta',
                            'Cuerda 4 (Re): Traste 1 (dedo 1)',
                            'Cuerda 5 (La): Traste 3 (dedo 3)',
                            'Cuerda 6 (Mi): No tocar (X)'
                        ]
                    }
                },
                G: {
                    major: {
                        name: 'Posición abierta',
                        positions: [
                            'Cuerda 1 (Mi): Traste 3 (dedo 4)',
                            'Cuerda 2 (Si): Abierta',
                            'Cuerda 3 (Sol): Abierta',
                            'Cuerda 4 (Re): Abierta',
                            'Cuerda 5 (La): Traste 2 (dedo 2)',
                            'Cuerda 6 (Mi): Traste 3 (dedo 3)'
                        ]
                    }
                }
            },
            piano: {
                C: {
                    major: {
                        name: 'Posición fundamental',
                        positions: [
                            'Mano derecha: C (pulgar), E (dedo medio), G (meñique)',
                            'Mano izquierda: C (meñique), E (dedo medio), G (pulgar)',
                            'Teclas: Do - Mi - Sol',
                            'Inversiones: E-G-C (1ª inv.), G-C-E (2ª inv.)'
                        ]
                    }
                }
            },
            bass: {
                C: {
                    major: {
                        name: 'Posición fundamental',
                        positions: [
                            'Nota raíz: Cuerda 3, Traste 3 (C)',
                            'Tercera: Cuerda 2, Traste 5 (E)',
                            'Quinta: Cuerda 1, Traste 3 (G)',
                            'Patrón: Fundamental - Tercera - Quinta'
                        ]
                    }
                }
            }
        };

        // Obtener posiciones o usar genéricas
        if (positions[instrument] && positions[instrument][root] && positions[instrument][root][type]) {
            return positions[instrument][root][type];
        }

        // Posiciones genéricas
        return {
            name: `${root} ${type}`,
            positions: [`Posiciones para ${root} ${type} en ${instrument} no disponibles`]
        };
    }

    // Función para actualizar posiciones de guitarra
    function updateGuitarPositions(chordData) {
        if (!guitarPositions) return;

        const positionInfo = guitarPositions.querySelector('.chord-position-info h4');
        const positionDetails = guitarPositions.querySelector('.chord-position-details');

        if (positionInfo) {
            positionInfo.textContent = chordData.name || `${currentRoot} ${currentType}`;
        }

        if (positionDetails && chordData.positions) {
            positionDetails.innerHTML = '';

            chordData.positions.forEach(position => {
                const positionDiv = document.createElement('div');
                positionDiv.className = 'chord-position-item';
                positionDiv.innerHTML = `<h5>${position.name}</h5>`;

                if (position.frets) {
                    const fretInfo = Object.entries(position.frets).map(([string, fret]) => {
                        const stringName = getStringName(string);
                        const fretDisplay = fret === 'x' ? 'No tocar' : `Traste ${fret}`;
                        return `<p><strong>Cuerda ${string} (${stringName}):</strong> ${fretDisplay}</p>`;
                    }).join('');
                    positionDiv.innerHTML += fretInfo;
                }

                if (position.difficulty) {
                    positionDiv.innerHTML += `<p><strong>Dificultad:</strong> ${position.difficulty}</p>`;
                }

                positionDetails.appendChild(positionDiv);
            });
        }
    }

    // Función auxiliar para obtener el nombre de la cuerda
    function getStringName(stringNumber) {
        const stringNames = {
            '1': 'Mi agudo',
            '2': 'Si',
            '3': 'Sol',
            '4': 'Re',
            '5': 'La',
            '6': 'Mi grave'
        };
        return stringNames[stringNumber] || `Cuerda ${stringNumber}`;
    }

    // Función para actualizar posiciones de piano
    function updatePianoPositions(chordData) {
        if (!pianoPositions) return;

        const positionInfo = pianoPositions.querySelector('.chord-position-info h4');
        const positionDetails = pianoPositions.querySelector('.chord-position-details');

        if (positionInfo) {
            positionInfo.textContent = chordData.name || `${currentRoot} ${currentType}`;
        }

        if (positionDetails && chordData.positions) {
            positionDetails.innerHTML = '';

            chordData.positions.forEach(position => {
                const positionDiv = document.createElement('div');
                positionDiv.className = 'chord-position-item';
                positionDiv.innerHTML = `<h5>${position.name}</h5>`;

                if (position.rightHand) {
                    positionDiv.innerHTML += `
                        <p><strong>Mano derecha:</strong> ${position.rightHand.keys.join(' - ')}</p>
                        <p><strong>Digitación MD:</strong> ${position.rightHand.fingering}</p>
                    `;
                }

                if (position.leftHand) {
                    positionDiv.innerHTML += `
                        <p><strong>Mano izquierda:</strong> ${position.leftHand.keys.join(' - ')}</p>
                        <p><strong>Digitación MI:</strong> ${position.leftHand.fingering}</p>
                    `;
                }

                positionDetails.appendChild(positionDiv);
            });
        }
    }

    // Función para actualizar posiciones de bajo
    function updateBassPositions(chordData) {
        if (!bassPositions) return;

        const positionInfo = bassPositions.querySelector('.chord-position-info h4');
        const positionDetails = bassPositions.querySelector('.chord-position-details');

        if (positionInfo) {
            positionInfo.textContent = chordData.name || `${currentRoot} ${currentType}`;
        }

        if (positionDetails && chordData.positions) {
            positionDetails.innerHTML = '';

            chordData.positions.forEach(position => {
                const positionDiv = document.createElement('div');
                positionDiv.className = 'chord-position-item';
                positionDiv.innerHTML = `<h5>${position.name}</h5>`;

                if (position.frets) {
                    const fretInfo = Object.entries(position.frets).map(([string, fret]) => {
                        const stringName = getBassStringName(string);
                        const fretDisplay = fret === 'x' ? 'No tocar' : `Traste ${fret}`;
                        return `<p><strong>Cuerda ${string} (${stringName}):</strong> ${fretDisplay}</p>`;
                    }).join('');
                    positionDiv.innerHTML += fretInfo;
                }

                if (position.difficulty) {
                    positionDiv.innerHTML += `<p><strong>Dificultad:</strong> ${position.difficulty}</p>`;
                }

                if (position.pattern) {
                    positionDiv.innerHTML += `<p><strong>Patrón:</strong> ${position.pattern}</p>`;
                }

                positionDetails.appendChild(positionDiv);
            });
        }
    }

    // Función auxiliar para obtener el nombre de la cuerda de bajo
    function getBassStringName(stringNumber) {
        const stringNames = {
            '1': 'Sol',
            '2': 'Re',
            '3': 'La',
            '4': 'Mi'
        };
        return stringNames[stringNumber] || `Cuerda ${stringNumber}`;
    }

    // Función para obtener el nombre legible del tipo de acorde
    function getChordTypeName(type) {
        const typeNames = {
            'major': 'Mayor',
            'minor': 'Menor',
            '7': '7',
            'maj7': 'maj7',
            'min7': 'min7',
            'dim': 'Disminuido',
            'aug': 'Aumentado',
            'sus2': 'sus2',
            'sus4': 'sus4',
            '9': '9',
            'add9': 'add9'
        };

        return typeNames[type] || type;
    }

    // Función para actualizar los acordes relacionados (simplificada)
    function updateRelatedChords() {
        const relatedGrid = document.querySelector('.related-chords-grid');
        if (!relatedGrid) return;

        // Mostrar mensaje de que no hay acordes relacionados disponibles
        relatedGrid.innerHTML = '<p>Selecciona un acorde para ver sus relaciones.</p>';
    }

    // Función para actualizar las variaciones (simplificada)
    function updateVariations() {
        const variationsGrid = document.querySelector('.variations-grid');
        if (!variationsGrid) return;

        // Mostrar mensaje de que no hay variaciones disponibles
        variationsGrid.innerHTML = '<p>No hay variaciones disponibles para este acorde.</p>';
    }



    // Event listeners para los botones de instrumento
    instrumentButtons.forEach(button => {
        button.addEventListener('click', function() {
            const instrument = this.getAttribute('data-instrument');
            changeInstrument(instrument);
        });
    });

    // Event listeners para los selectores de acorde
    if (rootNoteSelect) {
        rootNoteSelect.addEventListener('change', function() {
            currentRoot = this.value;
            updateChordDisplay();
        });
    }

    if (chordTypeSelect) {
        chordTypeSelect.addEventListener('change', function() {
            currentType = this.value;
            updateChordDisplay();
        });
    }



    // Función para actualizar los detalles del acorde
    function updateChordDetails() {
        // Elementos de la interfaz para los detalles del acorde
        const chordDescription = document.querySelector('.chord-description');
        const notesList = document.querySelector('.notes-list');
        const intervalsList = document.querySelector('.intervals-list');
        const progressionsList = document.querySelector('.progressions-list');

        // Información básica del acorde
        const chordInfo = {
            description: `El acorde ${currentRoot} ${getChordTypeName(currentType)} es uno de los acordes más utilizados en música.`,
            notes: getChordNotes(currentRoot, currentType),
            intervals: getChordIntervals(currentType),
            progressions: getChordProgressions(currentRoot, currentType)
        };

        // Actualizar descripción
        if (chordDescription) {
            chordDescription.textContent = chordInfo.description;
        }

        // Actualizar notas
        if (notesList) {
            notesList.textContent = chordInfo.notes.join(', ');
        }

        // Actualizar intervalos
        if (intervalsList) {
            intervalsList.textContent = chordInfo.intervals.join(', ');
        }

        // Actualizar progresiones
        if (progressionsList) {
            progressionsList.innerHTML = '';
            chordInfo.progressions.forEach(progression => {
                const li = document.createElement('li');
                li.textContent = progression;
                progressionsList.appendChild(li);
            });
        }
    }

    // Función para obtener las notas de un acorde
    function getChordNotes(root, type) {
        // Simplificado para este ejemplo
        const noteMap = {
            'C': ['C', 'E', 'G'],
            'D': ['D', 'F#', 'A'],
            'E': ['E', 'G#', 'B'],
            'F': ['F', 'A', 'C'],
            'G': ['G', 'B', 'D'],
            'A': ['A', 'C#', 'E'],
            'B': ['B', 'D#', 'F#']
        };

        // Modificar según el tipo
        let notes = noteMap[root] || ['C', 'E', 'G'];

        if (type === 'minor') {
            // Bajar la tercera un semitono
            if (notes[1].includes('#')) {
                notes[1] = notes[1].replace('#', '');
            } else {
                notes[1] = notes[1] === 'E' ? 'Eb' : notes[1] === 'A' ? 'Ab' : notes[1] === 'B' ? 'Bb' : notes[1];
            }
        }

        return notes;
    }

    // Función para obtener los intervalos de un acorde
    function getChordIntervals(type) {
        const intervalMap = {
            'major': ['1', '3', '5'],
            'minor': ['1', 'b3', '5'],
            'dim': ['1', 'b3', 'b5'],
            'aug': ['1', '3', '#5'],
            '7': ['1', '3', '5', 'b7'],
            'maj7': ['1', '3', '5', '7'],
            'min7': ['1', 'b3', '5', 'b7']
        };

        return intervalMap[type] || ['1', '3', '5'];
    }

    // Función para obtener progresiones comunes con este acorde
    function getChordProgressions(root, type) {
        // Simplificado para este ejemplo
        if (type === 'major') {
            return [
                `${root} - ${getNextChord(root, 4)} - ${getNextChord(root, 5)}`,
                `${root} - ${getNextChord(root, 5)} - ${getNextChord(root, 3, 'minor')} - ${getNextChord(root, 4)}`,
                `${root} - ${getNextChord(root, 6, 'minor')} - ${getNextChord(root, 4)} - ${getNextChord(root, 5)}`
            ];
        } else {
            return [
                `${root}m - ${getNextChord(root, 4)} - ${getNextChord(root, 5)}`,
                `${root}m - ${getNextChord(root, 3, 'major')} - ${getNextChord(root, 6, 'minor')}`,
                `${root}m - ${getNextChord(root, 5)} - ${getNextChord(root, 3, 'major')}`
            ];
        }
    }

    // Función para obtener el siguiente acorde en una progresión
    function getNextChord(root, interval, nextType = 'major') {
        const notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
        const rootIndex = notes.indexOf(root);

        if (rootIndex === -1) return 'C';

        const nextIndex = (rootIndex + interval - 1) % 12;
        const nextRoot = notes[nextIndex];

        return nextType === 'minor' ? `${nextRoot}m` : nextRoot;
    }

    // Función para buscar acordes
    function searchChords(query) {
        if (!query || query.trim() === '') {
            MusicApp.showAlert('Por favor, ingresa un término de búsqueda.', 'error');
            return;
        }

        // Mostrar el modal
        searchResultsModal.classList.add('show');

        // Mostrar indicador de carga
        searchResultsContainer.innerHTML = '<p class="loading"><i class="fas fa-spinner fa-spin"></i> Buscando acordes...</p>';

        // Realizar la búsqueda usando el servicio de datos
        const response = ChordDataService.searchChords(query);

        // Si la búsqueda fue exitosa
        if (response.success && response.data && response.data.length > 0) {
            // Limpiar el contenedor
            searchResultsContainer.innerHTML = '';

            // Mostrar los resultados
            response.data.forEach(result => {
                const card = document.createElement('div');
                card.className = 'search-result-card';

                // Crear una imagen en miniatura del acorde
                const imageHtml = result.imageUrl ?
                    `<div class="search-result-image">
                        <img src="${result.imageUrl}" alt="${result.name}" loading="lazy">
                    </div>` : '';

                card.innerHTML = `
                    ${imageHtml}
                    <h4>${result.name}</h4>
                    <p><strong>Instrumento:</strong> ${getInstrumentName(result.instrument)}</p>
                `;

                // Añadir evento para seleccionar el acorde
                card.addEventListener('click', function() {
                    // Extraer la nota raíz y el tipo del nombre del acorde
                    const [root, ...typeParts] = result.name.split(' ');
                    const type = mapChordTypeToValue(typeParts.join(' '));

                    // Cambiar al instrumento correspondiente
                    const instrumentButton = document.querySelector(`.btn-instrument[data-instrument="${result.instrument}"]`);
                    if (instrumentButton) {
                        instrumentButton.click();
                    }

                    // Seleccionar el acorde
                    if (rootNoteSelect) {
                        rootNoteSelect.value = root;
                    }

                    if (chordTypeSelect && type) {
                        chordTypeSelect.value = type;
                    }

                    // Actualizar la visualización
                    currentRoot = root;
                    currentType = type || 'major';
                    updateChordDisplay();

                    // Cerrar el modal
                    searchResultsModal.classList.remove('show');
                });

                searchResultsContainer.appendChild(card);
            });
        } else {
            // Mostrar mensaje de no resultados
            searchResultsContainer.innerHTML = '<p>No se encontraron acordes que coincidan con tu búsqueda.</p>';
        }
    }

    // Función para obtener el nombre del instrumento
    function getInstrumentName(instrument) {
        const names = {
            'guitar': 'Guitarra',
            'piano': 'Piano',
            'bass': 'Bajo'
        };

        return names[instrument] || instrument;
    }

    // Función para mapear el nombre del tipo de acorde a su valor en el select
    function mapChordTypeToValue(typeName) {
        const mapping = {
            'Mayor': 'major',
            'Menor': 'minor',
            'Disminuido': 'dim',
            'Aumentado': 'aug',
            '7': '7',
            'maj7': 'maj7',
            'min7': 'min7',
            'sus2': 'sus2',
            'sus4': 'sus4',
            '9': '9',
            'add9': 'add9'
        };

        return mapping[typeName] || 'major';
    }

    // Event listener para el botón de búsqueda
    if (searchButton) {
        searchButton.addEventListener('click', function() {
            const query = searchInput.value.trim();
            searchChords(query);
        });
    }

    // Event listener para buscar al presionar Enter en el campo de búsqueda
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const query = this.value.trim();
                searchChords(query);
            }
        });
    }

    // Event listener para cerrar el modal
    if (closeModalButton) {
        closeModalButton.addEventListener('click', function() {
            searchResultsModal.classList.remove('show');
        });
    }

    // Event listener para cerrar el modal al hacer clic fuera del contenido
    if (searchResultsModal) {
        searchResultsModal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.remove('show');
            }
        });
    }

    // Event listener para el botón de favoritos
    if (favoriteButton) {
        favoriteButton.addEventListener('click', function() {
            // Alternar el estado de favorito
            const result = ChordDataService.toggleFavorite(currentInstrument, currentRoot, currentType);

            // Actualizar el botón
            updateFavoriteButton();

            // Actualizar la lista de favoritos
            updateFavorites();

            // Mostrar mensaje
            MusicApp.showAlert(result.message, 'success');
        });
    }

    // Event listeners para las pestañas de teoría musical
    theoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');

            // Desactivar todas las pestañas y contenidos
            theoryTabs.forEach(t => t.classList.remove('active'));
            theoryTabContents.forEach(c => c.classList.remove('active'));

            // Activar la pestaña y contenido seleccionados
            this.classList.add('active');
            document.getElementById(`${tabId}-content`).classList.add('active');
        });
    });

    // Inicializar la visualización
    updateChordDisplay();

    // Inicializar la lista de favoritos
    updateFavorites();
});
